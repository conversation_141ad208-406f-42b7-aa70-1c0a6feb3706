#!/bin/bash

# FinOps API Endpoint Testing Script
echo "🧪 Starting comprehensive API endpoint testing..."

# Configuration
API_BASE="http://localhost:8080/api"
TOKEN="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VySWQiOiI2ODc0ZWEzNDgxZjE1NTNkMWYyZGRiM2YiLCJpYXQiOjE3NTI0OTI1OTksImV4cCI6MTc1MjU3ODk5OSwiYXVkIjoiZmlub3BzLXVzZXJzIiwiaXNzIjoiZmlub3BzLWFwcCJ9.pG2QX2LVMz2vJO4mY6ZHDLj2IB4f9XKeHg8kyjpoqYU"

# Test counters
TOTAL_TESTS=0
PASSED_TESTS=0
FAILED_TESTS=0

# Function to test endpoint
test_endpoint() {
    local method=$1
    local endpoint=$2
    local expected_status=$3
    local description=$4
    local data=$5
    
    TOTAL_TESTS=$((TOTAL_TESTS + 1))
    
    echo -n "Testing: $description... "
    
    if [ "$method" = "GET" ]; then
        response=$(curl -s -w "%{http_code}" -H "Authorization: Bearer $TOKEN" "$API_BASE$endpoint")
    elif [ "$method" = "POST" ]; then
        response=$(curl -s -w "%{http_code}" -X POST -H "Authorization: Bearer $TOKEN" -H "Content-Type: application/json" -d "$data" "$API_BASE$endpoint")
    elif [ "$method" = "PUT" ]; then
        response=$(curl -s -w "%{http_code}" -X PUT -H "Authorization: Bearer $TOKEN" -H "Content-Type: application/json" -d "$data" "$API_BASE$endpoint")
    elif [ "$method" = "DELETE" ]; then
        response=$(curl -s -w "%{http_code}" -X DELETE -H "Authorization: Bearer $TOKEN" "$API_BASE$endpoint")
    fi
    
    status_code="${response: -3}"
    
    if [ "$status_code" = "$expected_status" ]; then
        echo "✅ PASSED (Status: $status_code)"
        PASSED_TESTS=$((PASSED_TESTS + 1))
    else
        echo "❌ FAILED (Expected: $expected_status, Got: $status_code)"
        FAILED_TESTS=$((FAILED_TESTS + 1))
    fi
}

# Test unauthenticated endpoint
echo "🔓 Testing unauthenticated endpoints..."
response=$(curl -s -w "%{http_code}" "$API_BASE/finops/dashboard")
status_code="${response: -3}"
if [ "$status_code" = "401" ]; then
    echo "✅ Unauthenticated access properly blocked (401)"
    PASSED_TESTS=$((PASSED_TESTS + 1))
else
    echo "❌ Unauthenticated access not blocked (Got: $status_code)"
    FAILED_TESTS=$((FAILED_TESTS + 1))
fi
TOTAL_TESTS=$((TOTAL_TESTS + 1))

# Test authentication endpoints
echo "🔐 Testing authentication endpoints..."
test_endpoint "POST" "/auth/login" "200" "User login" '{"identifier":"<EMAIL>","password":"AdminPass123!"}'

# Test FinOps endpoints
echo "💰 Testing FinOps endpoints..."
test_endpoint "GET" "/finops/dashboard" "200" "FinOps dashboard"
test_endpoint "GET" "/finops/cost-analysis" "200" "Cost analysis"
test_endpoint "GET" "/finops/optimization-recommendations" "200" "Optimization recommendations"
test_endpoint "GET" "/finops/cost-report" "200" "Cost report (JSON)"
test_endpoint "GET" "/finops/cost-report?format=csv" "200" "Cost report (CSV)"

# Test budget endpoints
echo "📊 Testing budget endpoints..."
test_endpoint "GET" "/finops/budgets" "200" "List budgets"
test_endpoint "POST" "/finops/budgets" "201" "Create budget" '{"name":"Test Budget API","description":"API test budget","scope":{"type":"team","filters":{"teams":["test-team"]}},"period":{"type":"monthly","startDate":"2025-07-01T00:00:00.000Z","endDate":"2025-07-31T23:59:59.999Z"},"amounts":{"total":1000,"currency":"EUR"}}'

# Test todo endpoints
echo "📝 Testing todo endpoints..."
test_endpoint "GET" "/todos" "200" "List todos"
test_endpoint "POST" "/todos" "201" "Create todo" '{"title":"API Test Todo","description":"Todo created via API test","priority":"medium"}'

# Test health endpoints
echo "🏥 Testing health endpoints..."
response=$(curl -s -w "%{http_code}" "http://localhost:8080/health")
status_code="${response: -3}"
if [ "$status_code" = "200" ]; then
    echo "✅ Health check endpoint working (200)"
    PASSED_TESTS=$((PASSED_TESTS + 1))
else
    echo "❌ Health check endpoint failed (Got: $status_code)"
    FAILED_TESTS=$((FAILED_TESTS + 1))
fi
TOTAL_TESTS=$((TOTAL_TESTS + 1))

response=$(curl -s -w "%{http_code}" "http://localhost:8080/ready")
status_code="${response: -3}"
if [ "$status_code" = "200" ]; then
    echo "✅ Readiness check endpoint working (200)"
    PASSED_TESTS=$((PASSED_TESTS + 1))
else
    echo "❌ Readiness check endpoint failed (Got: $status_code)"
    FAILED_TESTS=$((FAILED_TESTS + 1))
fi
TOTAL_TESTS=$((TOTAL_TESTS + 1))

# Test error handling
echo "🚨 Testing error handling..."
test_endpoint "GET" "/non-existent-endpoint" "404" "Non-existent endpoint"

# Performance test
echo "⚡ Testing performance..."
echo -n "Performance test (5 concurrent requests)... "
start_time=$(date +%s.%N)
for i in {1..5}; do
    curl -s -H "Authorization: Bearer $TOKEN" "$API_BASE/finops/dashboard" > /dev/null &
done
wait
end_time=$(date +%s.%N)
duration=$(echo "$end_time - $start_time" | bc)
echo "✅ Completed in ${duration}s"

# Rate limiting test
echo "🛡️ Testing rate limiting..."
echo -n "Rate limiting test (6 failed login attempts)... "
failed_attempts=0
for i in {1..6}; do
    response=$(curl -s -w "%{http_code}" -X POST -H "Content-Type: application/json" -d '{"identifier":"wrong","password":"wrong"}' "$API_BASE/auth/login")
    status_code="${response: -3}"
    if [ "$status_code" = "429" ]; then
        echo "✅ Rate limiting triggered after $i attempts"
        PASSED_TESTS=$((PASSED_TESTS + 1))
        break
    fi
    failed_attempts=$i
done

if [ $failed_attempts -eq 6 ]; then
    echo "❌ Rate limiting not triggered"
    FAILED_TESTS=$((FAILED_TESTS + 1))
fi
TOTAL_TESTS=$((TOTAL_TESTS + 1))

# Summary
echo ""
echo "📊 TEST SUMMARY"
echo "==============="
echo "Total Tests: $TOTAL_TESTS"
echo "Passed: $PASSED_TESTS"
echo "Failed: $FAILED_TESTS"
echo "Success Rate: $(echo "scale=2; $PASSED_TESTS * 100 / $TOTAL_TESTS" | bc)%"

if [ $FAILED_TESTS -eq 0 ]; then
    echo "🎉 All tests passed!"
    exit 0
else
    echo "❌ Some tests failed!"
    exit 1
fi

{"level":"error","message":"❌ Erreur de connexion à la base de données: option buffermaxentries is not supported","stack":"MongoParseError: option buffermaxentries is not supported\n    at parseOptions (/Users/<USER>/Desktop/Projrt FINOPS/backend/node_modules/mongodb/lib/connection_string.js:273:15)\n    at new MongoClient (/Users/<USER>/Desktop/Projrt FINOPS/backend/node_modules/mongodb/lib/mongo_client.js:48:63)\n    at NativeConnection.createClient (/Users/<USER>/Desktop/Projrt FINOPS/backend/node_modules/mongoose/lib/drivers/node-mongodb-native/connection.js:288:14)\n    at NativeConnection.openUri (/Users/<USER>/Desktop/Projrt FINOPS/backend/node_modules/mongoose/lib/connection.js:766:34)\n    at Mongoose.connect (/Users/<USER>/Desktop/Projrt FINOPS/backend/node_modules/mongoose/lib/index.js:416:15)\n    at connectWithRetry (/Users/<USER>/Desktop/Projrt FINOPS/backend/server.js:98:6)\n    at Object.<anonymous> (/Users/<USER>/Desktop/Projrt FINOPS/backend/server.js:113:1)\n    at Module._compile (node:internal/modules/cjs/loader:1730:14)\n    at Object..js (node:internal/modules/cjs/loader:1895:10)\n    at Module.load (node:internal/modules/cjs/loader:1465:32)","timestamp":"2025-07-14 13:40:31:4031"}
{"level":"error","message":"MongoDB connection error: option buffermaxentries is not supported","stack":"MongoParseError: option buffermaxentries is not supported\n    at parseOptions (/Users/<USER>/Desktop/Projrt FINOPS/backend/node_modules/mongodb/lib/connection_string.js:273:15)\n    at new MongoClient (/Users/<USER>/Desktop/Projrt FINOPS/backend/node_modules/mongodb/lib/mongo_client.js:48:63)\n    at NativeConnection.createClient (/Users/<USER>/Desktop/Projrt FINOPS/backend/node_modules/mongoose/lib/drivers/node-mongodb-native/connection.js:288:14)\n    at NativeConnection.openUri (/Users/<USER>/Desktop/Projrt FINOPS/backend/node_modules/mongoose/lib/connection.js:766:34)\n    at Mongoose.connect (/Users/<USER>/Desktop/Projrt FINOPS/backend/node_modules/mongoose/lib/index.js:416:15)\n    at connectWithRetry (/Users/<USER>/Desktop/Projrt FINOPS/backend/server.js:98:6)\n    at Object.<anonymous> (/Users/<USER>/Desktop/Projrt FINOPS/backend/server.js:113:1)\n    at Module._compile (node:internal/modules/cjs/loader:1730:14)\n    at Object..js (node:internal/modules/cjs/loader:1895:10)\n    at Module.load (node:internal/modules/cjs/loader:1465:32)","timestamp":"2025-07-14 13:40:31:4031"}
{"level":"error","message":"❌ Erreur de connexion à la base de données: option buffermaxentries is not supported","stack":"MongoParseError: option buffermaxentries is not supported\n    at parseOptions (/Users/<USER>/Desktop/Projrt FINOPS/backend/node_modules/mongodb/lib/connection_string.js:273:15)\n    at new MongoClient (/Users/<USER>/Desktop/Projrt FINOPS/backend/node_modules/mongodb/lib/mongo_client.js:48:63)\n    at NativeConnection.createClient (/Users/<USER>/Desktop/Projrt FINOPS/backend/node_modules/mongoose/lib/drivers/node-mongodb-native/connection.js:288:14)\n    at NativeConnection.openUri (/Users/<USER>/Desktop/Projrt FINOPS/backend/node_modules/mongoose/lib/connection.js:766:34)\n    at Mongoose.connect (/Users/<USER>/Desktop/Projrt FINOPS/backend/node_modules/mongoose/lib/index.js:416:15)\n    at Timeout.connectWithRetry [as _onTimeout] (/Users/<USER>/Desktop/Projrt FINOPS/backend/server.js:98:6)\n    at listOnTimeout (node:internal/timers:588:17)\n    at process.processTimers (node:internal/timers:523:7)","timestamp":"2025-07-14 13:40:36:4036"}
{"level":"error","message":"MongoDB connection error: option buffermaxentries is not supported","stack":"MongoParseError: option buffermaxentries is not supported\n    at parseOptions (/Users/<USER>/Desktop/Projrt FINOPS/backend/node_modules/mongodb/lib/connection_string.js:273:15)\n    at new MongoClient (/Users/<USER>/Desktop/Projrt FINOPS/backend/node_modules/mongodb/lib/mongo_client.js:48:63)\n    at NativeConnection.createClient (/Users/<USER>/Desktop/Projrt FINOPS/backend/node_modules/mongoose/lib/drivers/node-mongodb-native/connection.js:288:14)\n    at NativeConnection.openUri (/Users/<USER>/Desktop/Projrt FINOPS/backend/node_modules/mongoose/lib/connection.js:766:34)\n    at Mongoose.connect (/Users/<USER>/Desktop/Projrt FINOPS/backend/node_modules/mongoose/lib/index.js:416:15)\n    at Timeout.connectWithRetry [as _onTimeout] (/Users/<USER>/Desktop/Projrt FINOPS/backend/server.js:98:6)\n    at listOnTimeout (node:internal/timers:588:17)\n    at process.processTimers (node:internal/timers:523:7)","timestamp":"2025-07-14 13:40:36:4036"}
{"level":"error","message":"❌ Erreur de connexion à la base de données: option buffermaxentries is not supported","stack":"MongoParseError: option buffermaxentries is not supported\n    at parseOptions (/Users/<USER>/Desktop/Projrt FINOPS/backend/node_modules/mongodb/lib/connection_string.js:273:15)\n    at new MongoClient (/Users/<USER>/Desktop/Projrt FINOPS/backend/node_modules/mongodb/lib/mongo_client.js:48:63)\n    at NativeConnection.createClient (/Users/<USER>/Desktop/Projrt FINOPS/backend/node_modules/mongoose/lib/drivers/node-mongodb-native/connection.js:288:14)\n    at NativeConnection.openUri (/Users/<USER>/Desktop/Projrt FINOPS/backend/node_modules/mongoose/lib/connection.js:766:34)\n    at Mongoose.connect (/Users/<USER>/Desktop/Projrt FINOPS/backend/node_modules/mongoose/lib/index.js:416:15)\n    at Timeout.connectWithRetry [as _onTimeout] (/Users/<USER>/Desktop/Projrt FINOPS/backend/server.js:98:6)\n    at listOnTimeout (node:internal/timers:588:17)\n    at process.processTimers (node:internal/timers:523:7)","timestamp":"2025-07-14 13:40:41:4041"}
{"level":"error","message":"MongoDB connection error: option buffermaxentries is not supported","stack":"MongoParseError: option buffermaxentries is not supported\n    at parseOptions (/Users/<USER>/Desktop/Projrt FINOPS/backend/node_modules/mongodb/lib/connection_string.js:273:15)\n    at new MongoClient (/Users/<USER>/Desktop/Projrt FINOPS/backend/node_modules/mongodb/lib/mongo_client.js:48:63)\n    at NativeConnection.createClient (/Users/<USER>/Desktop/Projrt FINOPS/backend/node_modules/mongoose/lib/drivers/node-mongodb-native/connection.js:288:14)\n    at NativeConnection.openUri (/Users/<USER>/Desktop/Projrt FINOPS/backend/node_modules/mongoose/lib/connection.js:766:34)\n    at Mongoose.connect (/Users/<USER>/Desktop/Projrt FINOPS/backend/node_modules/mongoose/lib/index.js:416:15)\n    at Timeout.connectWithRetry [as _onTimeout] (/Users/<USER>/Desktop/Projrt FINOPS/backend/server.js:98:6)\n    at listOnTimeout (node:internal/timers:588:17)\n    at process.processTimers (node:internal/timers:523:7)","timestamp":"2025-07-14 13:40:41:4041"}
{"level":"error","message":"❌ Erreur de connexion à la base de données: option buffermaxentries is not supported","stack":"MongoParseError: option buffermaxentries is not supported\n    at parseOptions (/Users/<USER>/Desktop/Projrt FINOPS/backend/node_modules/mongodb/lib/connection_string.js:273:15)\n    at new MongoClient (/Users/<USER>/Desktop/Projrt FINOPS/backend/node_modules/mongodb/lib/mongo_client.js:48:63)\n    at NativeConnection.createClient (/Users/<USER>/Desktop/Projrt FINOPS/backend/node_modules/mongoose/lib/drivers/node-mongodb-native/connection.js:288:14)\n    at NativeConnection.openUri (/Users/<USER>/Desktop/Projrt FINOPS/backend/node_modules/mongoose/lib/connection.js:766:34)\n    at Mongoose.connect (/Users/<USER>/Desktop/Projrt FINOPS/backend/node_modules/mongoose/lib/index.js:416:15)\n    at Timeout.connectWithRetry [as _onTimeout] (/Users/<USER>/Desktop/Projrt FINOPS/backend/server.js:98:6)\n    at listOnTimeout (node:internal/timers:588:17)\n    at process.processTimers (node:internal/timers:523:7)","timestamp":"2025-07-14 13:40:46:4046"}
{"level":"error","message":"MongoDB connection error: option buffermaxentries is not supported","stack":"MongoParseError: option buffermaxentries is not supported\n    at parseOptions (/Users/<USER>/Desktop/Projrt FINOPS/backend/node_modules/mongodb/lib/connection_string.js:273:15)\n    at new MongoClient (/Users/<USER>/Desktop/Projrt FINOPS/backend/node_modules/mongodb/lib/mongo_client.js:48:63)\n    at NativeConnection.createClient (/Users/<USER>/Desktop/Projrt FINOPS/backend/node_modules/mongoose/lib/drivers/node-mongodb-native/connection.js:288:14)\n    at NativeConnection.openUri (/Users/<USER>/Desktop/Projrt FINOPS/backend/node_modules/mongoose/lib/connection.js:766:34)\n    at Mongoose.connect (/Users/<USER>/Desktop/Projrt FINOPS/backend/node_modules/mongoose/lib/index.js:416:15)\n    at Timeout.connectWithRetry [as _onTimeout] (/Users/<USER>/Desktop/Projrt FINOPS/backend/server.js:98:6)\n    at listOnTimeout (node:internal/timers:588:17)\n    at process.processTimers (node:internal/timers:523:7)","timestamp":"2025-07-14 13:40:46:4046"}
{"level":"error","message":"❌ Erreur de connexion à la base de données: option buffermaxentries is not supported","stack":"MongoParseError: option buffermaxentries is not supported\n    at parseOptions (/Users/<USER>/Desktop/Projrt FINOPS/backend/node_modules/mongodb/lib/connection_string.js:273:15)\n    at new MongoClient (/Users/<USER>/Desktop/Projrt FINOPS/backend/node_modules/mongodb/lib/mongo_client.js:48:63)\n    at NativeConnection.createClient (/Users/<USER>/Desktop/Projrt FINOPS/backend/node_modules/mongoose/lib/drivers/node-mongodb-native/connection.js:288:14)\n    at NativeConnection.openUri (/Users/<USER>/Desktop/Projrt FINOPS/backend/node_modules/mongoose/lib/connection.js:766:34)\n    at Mongoose.connect (/Users/<USER>/Desktop/Projrt FINOPS/backend/node_modules/mongoose/lib/index.js:416:15)\n    at Timeout.connectWithRetry [as _onTimeout] (/Users/<USER>/Desktop/Projrt FINOPS/backend/server.js:98:6)\n    at listOnTimeout (node:internal/timers:588:17)\n    at process.processTimers (node:internal/timers:523:7)","timestamp":"2025-07-14 13:40:51:4051"}
{"level":"error","message":"MongoDB connection error: option buffermaxentries is not supported","stack":"MongoParseError: option buffermaxentries is not supported\n    at parseOptions (/Users/<USER>/Desktop/Projrt FINOPS/backend/node_modules/mongodb/lib/connection_string.js:273:15)\n    at new MongoClient (/Users/<USER>/Desktop/Projrt FINOPS/backend/node_modules/mongodb/lib/mongo_client.js:48:63)\n    at NativeConnection.createClient (/Users/<USER>/Desktop/Projrt FINOPS/backend/node_modules/mongoose/lib/drivers/node-mongodb-native/connection.js:288:14)\n    at NativeConnection.openUri (/Users/<USER>/Desktop/Projrt FINOPS/backend/node_modules/mongoose/lib/connection.js:766:34)\n    at Mongoose.connect (/Users/<USER>/Desktop/Projrt FINOPS/backend/node_modules/mongoose/lib/index.js:416:15)\n    at Timeout.connectWithRetry [as _onTimeout] (/Users/<USER>/Desktop/Projrt FINOPS/backend/server.js:98:6)\n    at listOnTimeout (node:internal/timers:588:17)\n    at process.processTimers (node:internal/timers:523:7)","timestamp":"2025-07-14 13:40:51:4051"}
{"level":"error","message":"❌ Erreur de connexion à la base de données: option buffermaxentries is not supported","stack":"MongoParseError: option buffermaxentries is not supported\n    at parseOptions (/Users/<USER>/Desktop/Projrt FINOPS/backend/node_modules/mongodb/lib/connection_string.js:273:15)\n    at new MongoClient (/Users/<USER>/Desktop/Projrt FINOPS/backend/node_modules/mongodb/lib/mongo_client.js:48:63)\n    at NativeConnection.createClient (/Users/<USER>/Desktop/Projrt FINOPS/backend/node_modules/mongoose/lib/drivers/node-mongodb-native/connection.js:288:14)\n    at NativeConnection.openUri (/Users/<USER>/Desktop/Projrt FINOPS/backend/node_modules/mongoose/lib/connection.js:766:34)\n    at Mongoose.connect (/Users/<USER>/Desktop/Projrt FINOPS/backend/node_modules/mongoose/lib/index.js:416:15)\n    at Timeout.connectWithRetry [as _onTimeout] (/Users/<USER>/Desktop/Projrt FINOPS/backend/server.js:98:6)\n    at listOnTimeout (node:internal/timers:588:17)\n    at process.processTimers (node:internal/timers:523:7)","timestamp":"2025-07-14 13:40:56:4056"}
{"level":"error","message":"MongoDB connection error: option buffermaxentries is not supported","stack":"MongoParseError: option buffermaxentries is not supported\n    at parseOptions (/Users/<USER>/Desktop/Projrt FINOPS/backend/node_modules/mongodb/lib/connection_string.js:273:15)\n    at new MongoClient (/Users/<USER>/Desktop/Projrt FINOPS/backend/node_modules/mongodb/lib/mongo_client.js:48:63)\n    at NativeConnection.createClient (/Users/<USER>/Desktop/Projrt FINOPS/backend/node_modules/mongoose/lib/drivers/node-mongodb-native/connection.js:288:14)\n    at NativeConnection.openUri (/Users/<USER>/Desktop/Projrt FINOPS/backend/node_modules/mongoose/lib/connection.js:766:34)\n    at Mongoose.connect (/Users/<USER>/Desktop/Projrt FINOPS/backend/node_modules/mongoose/lib/index.js:416:15)\n    at Timeout.connectWithRetry [as _onTimeout] (/Users/<USER>/Desktop/Projrt FINOPS/backend/server.js:98:6)\n    at listOnTimeout (node:internal/timers:588:17)\n    at process.processTimers (node:internal/timers:523:7)","timestamp":"2025-07-14 13:40:56:4056"}
{"level":"error","message":"❌ Erreur de connexion à la base de données: option buffermaxentries is not supported","stack":"MongoParseError: option buffermaxentries is not supported\n    at parseOptions (/Users/<USER>/Desktop/Projrt FINOPS/backend/node_modules/mongodb/lib/connection_string.js:273:15)\n    at new MongoClient (/Users/<USER>/Desktop/Projrt FINOPS/backend/node_modules/mongodb/lib/mongo_client.js:48:63)\n    at NativeConnection.createClient (/Users/<USER>/Desktop/Projrt FINOPS/backend/node_modules/mongoose/lib/drivers/node-mongodb-native/connection.js:288:14)\n    at NativeConnection.openUri (/Users/<USER>/Desktop/Projrt FINOPS/backend/node_modules/mongoose/lib/connection.js:766:34)\n    at Mongoose.connect (/Users/<USER>/Desktop/Projrt FINOPS/backend/node_modules/mongoose/lib/index.js:416:15)\n    at Timeout.connectWithRetry [as _onTimeout] (/Users/<USER>/Desktop/Projrt FINOPS/backend/server.js:98:6)\n    at listOnTimeout (node:internal/timers:588:17)\n    at process.processTimers (node:internal/timers:523:7)","timestamp":"2025-07-14 13:41:01:411"}
{"level":"error","message":"MongoDB connection error: option buffermaxentries is not supported","stack":"MongoParseError: option buffermaxentries is not supported\n    at parseOptions (/Users/<USER>/Desktop/Projrt FINOPS/backend/node_modules/mongodb/lib/connection_string.js:273:15)\n    at new MongoClient (/Users/<USER>/Desktop/Projrt FINOPS/backend/node_modules/mongodb/lib/mongo_client.js:48:63)\n    at NativeConnection.createClient (/Users/<USER>/Desktop/Projrt FINOPS/backend/node_modules/mongoose/lib/drivers/node-mongodb-native/connection.js:288:14)\n    at NativeConnection.openUri (/Users/<USER>/Desktop/Projrt FINOPS/backend/node_modules/mongoose/lib/connection.js:766:34)\n    at Mongoose.connect (/Users/<USER>/Desktop/Projrt FINOPS/backend/node_modules/mongoose/lib/index.js:416:15)\n    at Timeout.connectWithRetry [as _onTimeout] (/Users/<USER>/Desktop/Projrt FINOPS/backend/server.js:98:6)\n    at listOnTimeout (node:internal/timers:588:17)\n    at process.processTimers (node:internal/timers:523:7)","timestamp":"2025-07-14 13:41:01:411"}
{"level":"error","message":"❌ Erreur de connexion à la base de données: option buffermaxentries is not supported","stack":"MongoParseError: option buffermaxentries is not supported\n    at parseOptions (/Users/<USER>/Desktop/Projrt FINOPS/backend/node_modules/mongodb/lib/connection_string.js:273:15)\n    at new MongoClient (/Users/<USER>/Desktop/Projrt FINOPS/backend/node_modules/mongodb/lib/mongo_client.js:48:63)\n    at NativeConnection.createClient (/Users/<USER>/Desktop/Projrt FINOPS/backend/node_modules/mongoose/lib/drivers/node-mongodb-native/connection.js:288:14)\n    at NativeConnection.openUri (/Users/<USER>/Desktop/Projrt FINOPS/backend/node_modules/mongoose/lib/connection.js:766:34)\n    at Mongoose.connect (/Users/<USER>/Desktop/Projrt FINOPS/backend/node_modules/mongoose/lib/index.js:416:15)\n    at Timeout.connectWithRetry [as _onTimeout] (/Users/<USER>/Desktop/Projrt FINOPS/backend/server.js:98:6)\n    at listOnTimeout (node:internal/timers:588:17)\n    at process.processTimers (node:internal/timers:523:7)","timestamp":"2025-07-14 13:41:06:416"}
{"level":"error","message":"MongoDB connection error: option buffermaxentries is not supported","stack":"MongoParseError: option buffermaxentries is not supported\n    at parseOptions (/Users/<USER>/Desktop/Projrt FINOPS/backend/node_modules/mongodb/lib/connection_string.js:273:15)\n    at new MongoClient (/Users/<USER>/Desktop/Projrt FINOPS/backend/node_modules/mongodb/lib/mongo_client.js:48:63)\n    at NativeConnection.createClient (/Users/<USER>/Desktop/Projrt FINOPS/backend/node_modules/mongoose/lib/drivers/node-mongodb-native/connection.js:288:14)\n    at NativeConnection.openUri (/Users/<USER>/Desktop/Projrt FINOPS/backend/node_modules/mongoose/lib/connection.js:766:34)\n    at Mongoose.connect (/Users/<USER>/Desktop/Projrt FINOPS/backend/node_modules/mongoose/lib/index.js:416:15)\n    at Timeout.connectWithRetry [as _onTimeout] (/Users/<USER>/Desktop/Projrt FINOPS/backend/server.js:98:6)\n    at listOnTimeout (node:internal/timers:588:17)\n    at process.processTimers (node:internal/timers:523:7)","timestamp":"2025-07-14 13:41:06:416"}

{"level":"info","message":"🔄 Tentative de connexion à MongoDB...","timestamp":"2025-07-14 13:35:53:3553"}
{"level":"info","message":"🚀 Serveur en écoute sur le port 8080","timestamp":"2025-07-14 13:35:56:3556"}
{"level":"info","message":"📊 Health check: http://localhost:8080/health","timestamp":"2025-07-14 13:35:56:3556"}
{"level":"info","message":"🎯 Ready check: http://localhost:8080/ready","timestamp":"2025-07-14 13:35:56:3556"}
{"level":"info","message":"💓 Live check: http://localhost:8080/live","timestamp":"2025-07-14 13:35:56:3556"}
{"level":"info","message":"✅ Connecté à la base de données MongoDB","timestamp":"2025-07-14 13:35:57:3557"}
{"ip":"127.0.0.1","level":"http","message":"GET /api/finops/dashboard","method":"GET","timestamp":"2025-07-14 13:36:39:3639","url":"/api/finops/dashboard","userAgent":"curl/8.7.1"}
{"duration":7110,"ip":"127.0.0.1","level":"http","message":"GET /dashboard 200 - 7110ms","method":"GET","statusCode":200,"timestamp":"2025-07-14 13:36:46:3646","url":"/dashboard","userAgent":"curl/8.7.1","userId":"6874ea3481f1553d1f2ddb3f","username":"finopsadmin"}
{"level":"info","message":"🔄 Tentative de connexion à MongoDB...","timestamp":"2025-07-14 13:40:29:4029"}
{"level":"warn","message":"MongoDB disconnected","timestamp":"2025-07-14 13:40:30:4030"}
{"level":"info","message":"Performance monitoring initialized","timestamp":"2025-07-14 13:40:30:4030"}
{"level":"info","message":"🚀 Serveur en écoute sur le port 8080","timestamp":"2025-07-14 13:40:31:4031"}
{"level":"info","message":"📊 Health check: http://localhost:8080/health","timestamp":"2025-07-14 13:40:31:4031"}
{"level":"info","message":"🎯 Ready check: http://localhost:8080/ready","timestamp":"2025-07-14 13:40:31:4031"}
{"level":"info","message":"💓 Live check: http://localhost:8080/live","timestamp":"2025-07-14 13:40:31:4031"}
{"level":"error","message":"❌ Erreur de connexion à la base de données: option buffermaxentries is not supported","stack":"MongoParseError: option buffermaxentries is not supported\n    at parseOptions (/Users/<USER>/Desktop/Projrt FINOPS/backend/node_modules/mongodb/lib/connection_string.js:273:15)\n    at new MongoClient (/Users/<USER>/Desktop/Projrt FINOPS/backend/node_modules/mongodb/lib/mongo_client.js:48:63)\n    at NativeConnection.createClient (/Users/<USER>/Desktop/Projrt FINOPS/backend/node_modules/mongoose/lib/drivers/node-mongodb-native/connection.js:288:14)\n    at NativeConnection.openUri (/Users/<USER>/Desktop/Projrt FINOPS/backend/node_modules/mongoose/lib/connection.js:766:34)\n    at Mongoose.connect (/Users/<USER>/Desktop/Projrt FINOPS/backend/node_modules/mongoose/lib/index.js:416:15)\n    at connectWithRetry (/Users/<USER>/Desktop/Projrt FINOPS/backend/server.js:98:6)\n    at Object.<anonymous> (/Users/<USER>/Desktop/Projrt FINOPS/backend/server.js:113:1)\n    at Module._compile (node:internal/modules/cjs/loader:1730:14)\n    at Object..js (node:internal/modules/cjs/loader:1895:10)\n    at Module.load (node:internal/modules/cjs/loader:1465:32)","timestamp":"2025-07-14 13:40:31:4031"}
{"level":"info","message":"🔄 Nouvelle tentative dans 5 secondes...","timestamp":"2025-07-14 13:40:31:4031"}
{"level":"error","message":"MongoDB connection error: option buffermaxentries is not supported","stack":"MongoParseError: option buffermaxentries is not supported\n    at parseOptions (/Users/<USER>/Desktop/Projrt FINOPS/backend/node_modules/mongodb/lib/connection_string.js:273:15)\n    at new MongoClient (/Users/<USER>/Desktop/Projrt FINOPS/backend/node_modules/mongodb/lib/mongo_client.js:48:63)\n    at NativeConnection.createClient (/Users/<USER>/Desktop/Projrt FINOPS/backend/node_modules/mongoose/lib/drivers/node-mongodb-native/connection.js:288:14)\n    at NativeConnection.openUri (/Users/<USER>/Desktop/Projrt FINOPS/backend/node_modules/mongoose/lib/connection.js:766:34)\n    at Mongoose.connect (/Users/<USER>/Desktop/Projrt FINOPS/backend/node_modules/mongoose/lib/index.js:416:15)\n    at connectWithRetry (/Users/<USER>/Desktop/Projrt FINOPS/backend/server.js:98:6)\n    at Object.<anonymous> (/Users/<USER>/Desktop/Projrt FINOPS/backend/server.js:113:1)\n    at Module._compile (node:internal/modules/cjs/loader:1730:14)\n    at Object..js (node:internal/modules/cjs/loader:1895:10)\n    at Module.load (node:internal/modules/cjs/loader:1465:32)","timestamp":"2025-07-14 13:40:31:4031"}
{"level":"info","message":"🔄 Tentative de connexion à MongoDB...","timestamp":"2025-07-14 13:40:36:4036"}
{"level":"warn","message":"MongoDB disconnected","timestamp":"2025-07-14 13:40:36:4036"}
{"level":"error","message":"❌ Erreur de connexion à la base de données: option buffermaxentries is not supported","stack":"MongoParseError: option buffermaxentries is not supported\n    at parseOptions (/Users/<USER>/Desktop/Projrt FINOPS/backend/node_modules/mongodb/lib/connection_string.js:273:15)\n    at new MongoClient (/Users/<USER>/Desktop/Projrt FINOPS/backend/node_modules/mongodb/lib/mongo_client.js:48:63)\n    at NativeConnection.createClient (/Users/<USER>/Desktop/Projrt FINOPS/backend/node_modules/mongoose/lib/drivers/node-mongodb-native/connection.js:288:14)\n    at NativeConnection.openUri (/Users/<USER>/Desktop/Projrt FINOPS/backend/node_modules/mongoose/lib/connection.js:766:34)\n    at Mongoose.connect (/Users/<USER>/Desktop/Projrt FINOPS/backend/node_modules/mongoose/lib/index.js:416:15)\n    at Timeout.connectWithRetry [as _onTimeout] (/Users/<USER>/Desktop/Projrt FINOPS/backend/server.js:98:6)\n    at listOnTimeout (node:internal/timers:588:17)\n    at process.processTimers (node:internal/timers:523:7)","timestamp":"2025-07-14 13:40:36:4036"}
{"level":"info","message":"🔄 Nouvelle tentative dans 5 secondes...","timestamp":"2025-07-14 13:40:36:4036"}
{"level":"error","message":"MongoDB connection error: option buffermaxentries is not supported","stack":"MongoParseError: option buffermaxentries is not supported\n    at parseOptions (/Users/<USER>/Desktop/Projrt FINOPS/backend/node_modules/mongodb/lib/connection_string.js:273:15)\n    at new MongoClient (/Users/<USER>/Desktop/Projrt FINOPS/backend/node_modules/mongodb/lib/mongo_client.js:48:63)\n    at NativeConnection.createClient (/Users/<USER>/Desktop/Projrt FINOPS/backend/node_modules/mongoose/lib/drivers/node-mongodb-native/connection.js:288:14)\n    at NativeConnection.openUri (/Users/<USER>/Desktop/Projrt FINOPS/backend/node_modules/mongoose/lib/connection.js:766:34)\n    at Mongoose.connect (/Users/<USER>/Desktop/Projrt FINOPS/backend/node_modules/mongoose/lib/index.js:416:15)\n    at Timeout.connectWithRetry [as _onTimeout] (/Users/<USER>/Desktop/Projrt FINOPS/backend/server.js:98:6)\n    at listOnTimeout (node:internal/timers:588:17)\n    at process.processTimers (node:internal/timers:523:7)","timestamp":"2025-07-14 13:40:36:4036"}
{"level":"info","message":"🔄 Tentative de connexion à MongoDB...","timestamp":"2025-07-14 13:40:41:4041"}
{"level":"warn","message":"MongoDB disconnected","timestamp":"2025-07-14 13:40:41:4041"}
{"level":"error","message":"❌ Erreur de connexion à la base de données: option buffermaxentries is not supported","stack":"MongoParseError: option buffermaxentries is not supported\n    at parseOptions (/Users/<USER>/Desktop/Projrt FINOPS/backend/node_modules/mongodb/lib/connection_string.js:273:15)\n    at new MongoClient (/Users/<USER>/Desktop/Projrt FINOPS/backend/node_modules/mongodb/lib/mongo_client.js:48:63)\n    at NativeConnection.createClient (/Users/<USER>/Desktop/Projrt FINOPS/backend/node_modules/mongoose/lib/drivers/node-mongodb-native/connection.js:288:14)\n    at NativeConnection.openUri (/Users/<USER>/Desktop/Projrt FINOPS/backend/node_modules/mongoose/lib/connection.js:766:34)\n    at Mongoose.connect (/Users/<USER>/Desktop/Projrt FINOPS/backend/node_modules/mongoose/lib/index.js:416:15)\n    at Timeout.connectWithRetry [as _onTimeout] (/Users/<USER>/Desktop/Projrt FINOPS/backend/server.js:98:6)\n    at listOnTimeout (node:internal/timers:588:17)\n    at process.processTimers (node:internal/timers:523:7)","timestamp":"2025-07-14 13:40:41:4041"}
{"level":"info","message":"🔄 Nouvelle tentative dans 5 secondes...","timestamp":"2025-07-14 13:40:41:4041"}
{"level":"error","message":"MongoDB connection error: option buffermaxentries is not supported","stack":"MongoParseError: option buffermaxentries is not supported\n    at parseOptions (/Users/<USER>/Desktop/Projrt FINOPS/backend/node_modules/mongodb/lib/connection_string.js:273:15)\n    at new MongoClient (/Users/<USER>/Desktop/Projrt FINOPS/backend/node_modules/mongodb/lib/mongo_client.js:48:63)\n    at NativeConnection.createClient (/Users/<USER>/Desktop/Projrt FINOPS/backend/node_modules/mongoose/lib/drivers/node-mongodb-native/connection.js:288:14)\n    at NativeConnection.openUri (/Users/<USER>/Desktop/Projrt FINOPS/backend/node_modules/mongoose/lib/connection.js:766:34)\n    at Mongoose.connect (/Users/<USER>/Desktop/Projrt FINOPS/backend/node_modules/mongoose/lib/index.js:416:15)\n    at Timeout.connectWithRetry [as _onTimeout] (/Users/<USER>/Desktop/Projrt FINOPS/backend/server.js:98:6)\n    at listOnTimeout (node:internal/timers:588:17)\n    at process.processTimers (node:internal/timers:523:7)","timestamp":"2025-07-14 13:40:41:4041"}
{"level":"info","message":"🔄 Tentative de connexion à MongoDB...","timestamp":"2025-07-14 13:40:46:4046"}
{"level":"warn","message":"MongoDB disconnected","timestamp":"2025-07-14 13:40:46:4046"}
{"level":"error","message":"❌ Erreur de connexion à la base de données: option buffermaxentries is not supported","stack":"MongoParseError: option buffermaxentries is not supported\n    at parseOptions (/Users/<USER>/Desktop/Projrt FINOPS/backend/node_modules/mongodb/lib/connection_string.js:273:15)\n    at new MongoClient (/Users/<USER>/Desktop/Projrt FINOPS/backend/node_modules/mongodb/lib/mongo_client.js:48:63)\n    at NativeConnection.createClient (/Users/<USER>/Desktop/Projrt FINOPS/backend/node_modules/mongoose/lib/drivers/node-mongodb-native/connection.js:288:14)\n    at NativeConnection.openUri (/Users/<USER>/Desktop/Projrt FINOPS/backend/node_modules/mongoose/lib/connection.js:766:34)\n    at Mongoose.connect (/Users/<USER>/Desktop/Projrt FINOPS/backend/node_modules/mongoose/lib/index.js:416:15)\n    at Timeout.connectWithRetry [as _onTimeout] (/Users/<USER>/Desktop/Projrt FINOPS/backend/server.js:98:6)\n    at listOnTimeout (node:internal/timers:588:17)\n    at process.processTimers (node:internal/timers:523:7)","timestamp":"2025-07-14 13:40:46:4046"}
{"level":"info","message":"🔄 Nouvelle tentative dans 5 secondes...","timestamp":"2025-07-14 13:40:46:4046"}
{"level":"error","message":"MongoDB connection error: option buffermaxentries is not supported","stack":"MongoParseError: option buffermaxentries is not supported\n    at parseOptions (/Users/<USER>/Desktop/Projrt FINOPS/backend/node_modules/mongodb/lib/connection_string.js:273:15)\n    at new MongoClient (/Users/<USER>/Desktop/Projrt FINOPS/backend/node_modules/mongodb/lib/mongo_client.js:48:63)\n    at NativeConnection.createClient (/Users/<USER>/Desktop/Projrt FINOPS/backend/node_modules/mongoose/lib/drivers/node-mongodb-native/connection.js:288:14)\n    at NativeConnection.openUri (/Users/<USER>/Desktop/Projrt FINOPS/backend/node_modules/mongoose/lib/connection.js:766:34)\n    at Mongoose.connect (/Users/<USER>/Desktop/Projrt FINOPS/backend/node_modules/mongoose/lib/index.js:416:15)\n    at Timeout.connectWithRetry [as _onTimeout] (/Users/<USER>/Desktop/Projrt FINOPS/backend/server.js:98:6)\n    at listOnTimeout (node:internal/timers:588:17)\n    at process.processTimers (node:internal/timers:523:7)","timestamp":"2025-07-14 13:40:46:4046"}
{"level":"info","message":"🔄 Tentative de connexion à MongoDB...","timestamp":"2025-07-14 13:40:51:4051"}
{"level":"warn","message":"MongoDB disconnected","timestamp":"2025-07-14 13:40:51:4051"}
{"level":"error","message":"❌ Erreur de connexion à la base de données: option buffermaxentries is not supported","stack":"MongoParseError: option buffermaxentries is not supported\n    at parseOptions (/Users/<USER>/Desktop/Projrt FINOPS/backend/node_modules/mongodb/lib/connection_string.js:273:15)\n    at new MongoClient (/Users/<USER>/Desktop/Projrt FINOPS/backend/node_modules/mongodb/lib/mongo_client.js:48:63)\n    at NativeConnection.createClient (/Users/<USER>/Desktop/Projrt FINOPS/backend/node_modules/mongoose/lib/drivers/node-mongodb-native/connection.js:288:14)\n    at NativeConnection.openUri (/Users/<USER>/Desktop/Projrt FINOPS/backend/node_modules/mongoose/lib/connection.js:766:34)\n    at Mongoose.connect (/Users/<USER>/Desktop/Projrt FINOPS/backend/node_modules/mongoose/lib/index.js:416:15)\n    at Timeout.connectWithRetry [as _onTimeout] (/Users/<USER>/Desktop/Projrt FINOPS/backend/server.js:98:6)\n    at listOnTimeout (node:internal/timers:588:17)\n    at process.processTimers (node:internal/timers:523:7)","timestamp":"2025-07-14 13:40:51:4051"}
{"level":"info","message":"🔄 Nouvelle tentative dans 5 secondes...","timestamp":"2025-07-14 13:40:51:4051"}
{"level":"error","message":"MongoDB connection error: option buffermaxentries is not supported","stack":"MongoParseError: option buffermaxentries is not supported\n    at parseOptions (/Users/<USER>/Desktop/Projrt FINOPS/backend/node_modules/mongodb/lib/connection_string.js:273:15)\n    at new MongoClient (/Users/<USER>/Desktop/Projrt FINOPS/backend/node_modules/mongodb/lib/mongo_client.js:48:63)\n    at NativeConnection.createClient (/Users/<USER>/Desktop/Projrt FINOPS/backend/node_modules/mongoose/lib/drivers/node-mongodb-native/connection.js:288:14)\n    at NativeConnection.openUri (/Users/<USER>/Desktop/Projrt FINOPS/backend/node_modules/mongoose/lib/connection.js:766:34)\n    at Mongoose.connect (/Users/<USER>/Desktop/Projrt FINOPS/backend/node_modules/mongoose/lib/index.js:416:15)\n    at Timeout.connectWithRetry [as _onTimeout] (/Users/<USER>/Desktop/Projrt FINOPS/backend/server.js:98:6)\n    at listOnTimeout (node:internal/timers:588:17)\n    at process.processTimers (node:internal/timers:523:7)","timestamp":"2025-07-14 13:40:51:4051"}
{"level":"info","message":"🔄 Tentative de connexion à MongoDB...","timestamp":"2025-07-14 13:40:56:4056"}
{"level":"warn","message":"MongoDB disconnected","timestamp":"2025-07-14 13:40:56:4056"}
{"level":"error","message":"❌ Erreur de connexion à la base de données: option buffermaxentries is not supported","stack":"MongoParseError: option buffermaxentries is not supported\n    at parseOptions (/Users/<USER>/Desktop/Projrt FINOPS/backend/node_modules/mongodb/lib/connection_string.js:273:15)\n    at new MongoClient (/Users/<USER>/Desktop/Projrt FINOPS/backend/node_modules/mongodb/lib/mongo_client.js:48:63)\n    at NativeConnection.createClient (/Users/<USER>/Desktop/Projrt FINOPS/backend/node_modules/mongoose/lib/drivers/node-mongodb-native/connection.js:288:14)\n    at NativeConnection.openUri (/Users/<USER>/Desktop/Projrt FINOPS/backend/node_modules/mongoose/lib/connection.js:766:34)\n    at Mongoose.connect (/Users/<USER>/Desktop/Projrt FINOPS/backend/node_modules/mongoose/lib/index.js:416:15)\n    at Timeout.connectWithRetry [as _onTimeout] (/Users/<USER>/Desktop/Projrt FINOPS/backend/server.js:98:6)\n    at listOnTimeout (node:internal/timers:588:17)\n    at process.processTimers (node:internal/timers:523:7)","timestamp":"2025-07-14 13:40:56:4056"}
{"level":"info","message":"🔄 Nouvelle tentative dans 5 secondes...","timestamp":"2025-07-14 13:40:56:4056"}
{"level":"error","message":"MongoDB connection error: option buffermaxentries is not supported","stack":"MongoParseError: option buffermaxentries is not supported\n    at parseOptions (/Users/<USER>/Desktop/Projrt FINOPS/backend/node_modules/mongodb/lib/connection_string.js:273:15)\n    at new MongoClient (/Users/<USER>/Desktop/Projrt FINOPS/backend/node_modules/mongodb/lib/mongo_client.js:48:63)\n    at NativeConnection.createClient (/Users/<USER>/Desktop/Projrt FINOPS/backend/node_modules/mongoose/lib/drivers/node-mongodb-native/connection.js:288:14)\n    at NativeConnection.openUri (/Users/<USER>/Desktop/Projrt FINOPS/backend/node_modules/mongoose/lib/connection.js:766:34)\n    at Mongoose.connect (/Users/<USER>/Desktop/Projrt FINOPS/backend/node_modules/mongoose/lib/index.js:416:15)\n    at Timeout.connectWithRetry [as _onTimeout] (/Users/<USER>/Desktop/Projrt FINOPS/backend/server.js:98:6)\n    at listOnTimeout (node:internal/timers:588:17)\n    at process.processTimers (node:internal/timers:523:7)","timestamp":"2025-07-14 13:40:56:4056"}
{"level":"info","message":"🔄 Tentative de connexion à MongoDB...","timestamp":"2025-07-14 13:41:01:411"}
{"level":"warn","message":"MongoDB disconnected","timestamp":"2025-07-14 13:41:01:411"}
{"level":"error","message":"❌ Erreur de connexion à la base de données: option buffermaxentries is not supported","stack":"MongoParseError: option buffermaxentries is not supported\n    at parseOptions (/Users/<USER>/Desktop/Projrt FINOPS/backend/node_modules/mongodb/lib/connection_string.js:273:15)\n    at new MongoClient (/Users/<USER>/Desktop/Projrt FINOPS/backend/node_modules/mongodb/lib/mongo_client.js:48:63)\n    at NativeConnection.createClient (/Users/<USER>/Desktop/Projrt FINOPS/backend/node_modules/mongoose/lib/drivers/node-mongodb-native/connection.js:288:14)\n    at NativeConnection.openUri (/Users/<USER>/Desktop/Projrt FINOPS/backend/node_modules/mongoose/lib/connection.js:766:34)\n    at Mongoose.connect (/Users/<USER>/Desktop/Projrt FINOPS/backend/node_modules/mongoose/lib/index.js:416:15)\n    at Timeout.connectWithRetry [as _onTimeout] (/Users/<USER>/Desktop/Projrt FINOPS/backend/server.js:98:6)\n    at listOnTimeout (node:internal/timers:588:17)\n    at process.processTimers (node:internal/timers:523:7)","timestamp":"2025-07-14 13:41:01:411"}
{"level":"info","message":"🔄 Nouvelle tentative dans 5 secondes...","timestamp":"2025-07-14 13:41:01:411"}
{"level":"error","message":"MongoDB connection error: option buffermaxentries is not supported","stack":"MongoParseError: option buffermaxentries is not supported\n    at parseOptions (/Users/<USER>/Desktop/Projrt FINOPS/backend/node_modules/mongodb/lib/connection_string.js:273:15)\n    at new MongoClient (/Users/<USER>/Desktop/Projrt FINOPS/backend/node_modules/mongodb/lib/mongo_client.js:48:63)\n    at NativeConnection.createClient (/Users/<USER>/Desktop/Projrt FINOPS/backend/node_modules/mongoose/lib/drivers/node-mongodb-native/connection.js:288:14)\n    at NativeConnection.openUri (/Users/<USER>/Desktop/Projrt FINOPS/backend/node_modules/mongoose/lib/connection.js:766:34)\n    at Mongoose.connect (/Users/<USER>/Desktop/Projrt FINOPS/backend/node_modules/mongoose/lib/index.js:416:15)\n    at Timeout.connectWithRetry [as _onTimeout] (/Users/<USER>/Desktop/Projrt FINOPS/backend/server.js:98:6)\n    at listOnTimeout (node:internal/timers:588:17)\n    at process.processTimers (node:internal/timers:523:7)","timestamp":"2025-07-14 13:41:01:411"}
{"level":"info","message":"🔄 Tentative de connexion à MongoDB...","timestamp":"2025-07-14 13:41:06:416"}
{"level":"warn","message":"MongoDB disconnected","timestamp":"2025-07-14 13:41:06:416"}
{"level":"error","message":"❌ Erreur de connexion à la base de données: option buffermaxentries is not supported","stack":"MongoParseError: option buffermaxentries is not supported\n    at parseOptions (/Users/<USER>/Desktop/Projrt FINOPS/backend/node_modules/mongodb/lib/connection_string.js:273:15)\n    at new MongoClient (/Users/<USER>/Desktop/Projrt FINOPS/backend/node_modules/mongodb/lib/mongo_client.js:48:63)\n    at NativeConnection.createClient (/Users/<USER>/Desktop/Projrt FINOPS/backend/node_modules/mongoose/lib/drivers/node-mongodb-native/connection.js:288:14)\n    at NativeConnection.openUri (/Users/<USER>/Desktop/Projrt FINOPS/backend/node_modules/mongoose/lib/connection.js:766:34)\n    at Mongoose.connect (/Users/<USER>/Desktop/Projrt FINOPS/backend/node_modules/mongoose/lib/index.js:416:15)\n    at Timeout.connectWithRetry [as _onTimeout] (/Users/<USER>/Desktop/Projrt FINOPS/backend/server.js:98:6)\n    at listOnTimeout (node:internal/timers:588:17)\n    at process.processTimers (node:internal/timers:523:7)","timestamp":"2025-07-14 13:41:06:416"}
{"level":"info","message":"🔄 Nouvelle tentative dans 5 secondes...","timestamp":"2025-07-14 13:41:06:416"}
{"level":"error","message":"MongoDB connection error: option buffermaxentries is not supported","stack":"MongoParseError: option buffermaxentries is not supported\n    at parseOptions (/Users/<USER>/Desktop/Projrt FINOPS/backend/node_modules/mongodb/lib/connection_string.js:273:15)\n    at new MongoClient (/Users/<USER>/Desktop/Projrt FINOPS/backend/node_modules/mongodb/lib/mongo_client.js:48:63)\n    at NativeConnection.createClient (/Users/<USER>/Desktop/Projrt FINOPS/backend/node_modules/mongoose/lib/drivers/node-mongodb-native/connection.js:288:14)\n    at NativeConnection.openUri (/Users/<USER>/Desktop/Projrt FINOPS/backend/node_modules/mongoose/lib/connection.js:766:34)\n    at Mongoose.connect (/Users/<USER>/Desktop/Projrt FINOPS/backend/node_modules/mongoose/lib/index.js:416:15)\n    at Timeout.connectWithRetry [as _onTimeout] (/Users/<USER>/Desktop/Projrt FINOPS/backend/server.js:98:6)\n    at listOnTimeout (node:internal/timers:588:17)\n    at process.processTimers (node:internal/timers:523:7)","timestamp":"2025-07-14 13:41:06:416"}

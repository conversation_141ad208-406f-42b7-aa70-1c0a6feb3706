module.exports = app => {
  const todos = require('../controllers/todo.controller.js');
  const { todoValidation } = require('../middleware/validation.js');

  const router = require('express').Router();

  // Créer un nouveau todo
  router.post('/', todoValidation.create, todos.create);

  // Récupérer tous les todos
  router.get('/', todos.findAll);

  // Récupérer tous les todos complétés
  router.get('/completed', todos.findAllCompleted);

  // Récupérer un todo par id
  router.get('/:id', todoValidation.getId, todos.findOne);

  // Mettre à jour un todo par id
  router.put('/:id', todoValidation.update, todos.update);

  // Supprimer un todo par id
  router.delete('/:id', todoValidation.getId, todos.delete);

  // Supprimer tous les todos (ajout d'une protection)
  router.delete('/', todos.deleteAll);

  // Recherche par titre
  router.get('/search/:title', todos.findByTitle);

  // Statistiques
  router.get('/stats/summary', todos.getStats);

  app.use('/api/todos', router);
};

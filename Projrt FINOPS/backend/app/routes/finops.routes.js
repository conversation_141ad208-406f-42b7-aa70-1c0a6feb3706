const express = require('express');
const finopsController = require('../controllers/finops.controller.js');
const budgetController = require('../controllers/budget.controller.js');
const { verifyToken, requireFinOpsAccess } = require('../middleware/auth.middleware.js');
const { body, query } = require('express-validator');

const router = express.Router();

// All FinOps routes require authentication and FinOps access
router.use(verifyToken);
router.use(requireFinOpsAccess);

// FinOps Dashboard and Analytics Routes
router.get('/dashboard', 
  [
    query('startDate').optional().isISO8601().withMessage('Invalid start date format'),
    query('endDate').optional().isISO8601().withMessage('Invalid end date format'),
    query('team').optional().trim().escape(),
    query('project').optional().trim().escape(),
    query('environment').optional().isIn(['development', 'staging', 'production', 'testing'])
  ],
  finopsController.getDashboard
);

router.get('/cost-analysis',
  [
    query('startDate').optional().isISO8601().withMessage('Invalid start date format'),
    query('endDate').optional().isISO8601().withMessage('Invalid end date format'),
    query('groupBy').optional().isIn(['hour', 'day', 'week', 'month']).withMessage('Invalid groupBy value'),
    query('filterBy').optional().isIn(['team', 'project', 'namespace', 'resourceType', 'environment']),
    query('filterValue').optional().trim().escape()
  ],
  finopsController.getCostAnalysis
);

router.get('/resource-utilization',
  [
    query('startDate').optional().isISO8601().withMessage('Invalid start date format'),
    query('endDate').optional().isISO8601().withMessage('Invalid end date format'),
    query('resourceType').optional().trim().escape(),
    query('namespace').optional().trim().escape()
  ],
  finopsController.getResourceUtilization
);

router.get('/optimization-recommendations',
  [
    query('days').optional().isInt({ min: 1, max: 90 }).withMessage('Days must be between 1 and 90')
  ],
  finopsController.getOptimizationRecommendations
);

router.get('/cost-report',
  [
    query('startDate').optional().isISO8601().withMessage('Invalid start date format'),
    query('endDate').optional().isISO8601().withMessage('Invalid end date format'),
    query('format').optional().isIn(['json', 'csv']).withMessage('Format must be json or csv'),
    query('includeDetails').optional().isBoolean(),
    query('groupBy').optional().isIn(['team', 'project', 'namespace', 'resourceType'])
  ],
  finopsController.generateCostReport
);

// Budget Management Routes
router.post('/budgets',
  [
    body('name')
      .trim()
      .notEmpty()
      .withMessage('Budget name is required')
      .isLength({ max: 100 })
      .withMessage('Budget name cannot exceed 100 characters')
      .escape(),
    body('description')
      .optional()
      .trim()
      .isLength({ max: 500 })
      .withMessage('Description cannot exceed 500 characters')
      .escape(),
    body('scope.type')
      .isIn(['global', 'cluster', 'namespace', 'team', 'project', 'costCenter'])
      .withMessage('Invalid scope type'),
    body('period.type')
      .isIn(['monthly', 'quarterly', 'yearly', 'custom'])
      .withMessage('Invalid period type'),
    body('period.startDate')
      .isISO8601()
      .withMessage('Invalid start date format'),
    body('period.endDate')
      .isISO8601()
      .withMessage('Invalid end date format'),
    body('amounts.total')
      .isFloat({ min: 0 })
      .withMessage('Total amount must be a positive number'),
    body('amounts.currency')
      .optional()
      .isIn(['EUR', 'USD', 'GBP', 'JPY'])
      .withMessage('Invalid currency')
  ],
  budgetController.create
);

router.get('/budgets',
  [
    query('status').optional().isIn(['active', 'inactive', 'expired', 'draft']),
    query('team').optional().trim().escape(),
    query('project').optional().trim().escape(),
    query('page').optional().isInt({ min: 1 }).withMessage('Page must be a positive integer'),
    query('limit').optional().isInt({ min: 1, max: 100 }).withMessage('Limit must be between 1 and 100')
  ],
  budgetController.findAll
);

router.get('/budgets/:id',
  budgetController.findOne
);

router.put('/budgets/:id',
  [
    body('name')
      .optional()
      .trim()
      .notEmpty()
      .withMessage('Budget name cannot be empty')
      .isLength({ max: 100 })
      .withMessage('Budget name cannot exceed 100 characters')
      .escape(),
    body('description')
      .optional()
      .trim()
      .isLength({ max: 500 })
      .withMessage('Description cannot exceed 500 characters')
      .escape(),
    body('amounts.total')
      .optional()
      .isFloat({ min: 0 })
      .withMessage('Total amount must be a positive number'),
    body('status')
      .optional()
      .isIn(['active', 'inactive', 'expired', 'draft'])
      .withMessage('Invalid status')
  ],
  budgetController.update
);

router.delete('/budgets/:id',
  budgetController.delete
);

router.get('/budgets/alerts/active',
  budgetController.getAlerts
);

module.exports = router;

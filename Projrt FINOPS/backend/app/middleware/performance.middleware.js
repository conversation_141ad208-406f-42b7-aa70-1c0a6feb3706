const logger = require('../config/logger.config.js');

// Performance monitoring middleware
const performanceMonitor = (req, res, next) => {
  const start = process.hrtime.bigint();
  const startMemory = process.memoryUsage();
  
  // Add performance tracking to request
  req.performance = {
    start: start,
    startMemory: startMemory,
    checkpoints: []
  };
  
  // Add checkpoint method to request
  req.addCheckpoint = (name) => {
    const now = process.hrtime.bigint();
    const duration = Number(now - start) / 1000000; // Convert to milliseconds
    
    req.performance.checkpoints.push({
      name,
      timestamp: now,
      duration
    });
  };
  
  // Override res.end to capture final metrics
  const originalEnd = res.end;
  res.end = function(...args) {
    const end = process.hrtime.bigint();
    const endMemory = process.memoryUsage();
    const totalDuration = Number(end - start) / 1000000; // Convert to milliseconds
    
    // Calculate memory usage
    const memoryDelta = {
      rss: endMemory.rss - startMemory.rss,
      heapUsed: endMemory.heapUsed - startMemory.heapUsed,
      heapTotal: endMemory.heapTotal - startMemory.heapTotal,
      external: endMemory.external - startMemory.external
    };
    
    // Log performance metrics
    const performanceData = {
      method: req.method,
      url: req.url,
      statusCode: res.statusCode,
      duration: totalDuration,
      memory: {
        start: startMemory,
        end: endMemory,
        delta: memoryDelta
      },
      checkpoints: req.performance.checkpoints,
      userId: req.user?.userId,
      userAgent: req.get('User-Agent'),
      ip: req.ip
    };
    
    // Log slow requests
    if (totalDuration > 1000) {
      logger.performance('Slow request detected', totalDuration, performanceData);
    } else if (totalDuration > 500) {
      logger.performance('Moderate request duration', totalDuration, performanceData);
    }
    
    // Log high memory usage
    if (memoryDelta.heapUsed > 50 * 1024 * 1024) { // 50MB
      logger.performance('High memory usage detected', memoryDelta.heapUsed, performanceData);
    }
    
    // Add performance headers
    res.set({
      'X-Response-Time': `${totalDuration.toFixed(2)}ms`,
      'X-Memory-Usage': `${(endMemory.heapUsed / 1024 / 1024).toFixed(2)}MB`
    });
    
    originalEnd.apply(this, args);
  };
  
  next();
};

// Database query performance monitoring
const dbPerformanceMonitor = (schema) => {
  // Pre-hook for all find operations
  schema.pre(/^find/, function() {
    this._startTime = Date.now();
    this._operation = this.op;
  });
  
  // Post-hook for all find operations
  schema.post(/^find/, function(result) {
    if (this._startTime) {
      const duration = Date.now() - this._startTime;
      
      if (duration > 100) { // Log queries taking more than 100ms
        logger.performance('Database query performance', duration, {
          operation: this._operation,
          collection: this.model.collection.name,
          query: this.getQuery(),
          options: this.getOptions(),
          resultCount: Array.isArray(result) ? result.length : (result ? 1 : 0)
        });
      }
    }
  });
  
  // Pre-hook for save operations
  schema.pre('save', function() {
    this._saveStartTime = Date.now();
  });
  
  // Post-hook for save operations
  schema.post('save', function() {
    if (this._saveStartTime) {
      const duration = Date.now() - this._saveStartTime;
      
      if (duration > 100) {
        logger.performance('Database save performance', duration, {
          operation: 'save',
          collection: this.constructor.collection.name,
          documentId: this._id
        });
      }
    }
  });
};

// API endpoint performance tracking
const endpointPerformanceTracker = () => {
  const endpointStats = new Map();
  
  return (req, res, next) => {
    const endpoint = `${req.method} ${req.route?.path || req.url}`;
    const start = Date.now();
    
    res.on('finish', () => {
      const duration = Date.now() - start;
      
      if (!endpointStats.has(endpoint)) {
        endpointStats.set(endpoint, {
          count: 0,
          totalDuration: 0,
          minDuration: Infinity,
          maxDuration: 0,
          errors: 0
        });
      }
      
      const stats = endpointStats.get(endpoint);
      stats.count++;
      stats.totalDuration += duration;
      stats.minDuration = Math.min(stats.minDuration, duration);
      stats.maxDuration = Math.max(stats.maxDuration, duration);
      
      if (res.statusCode >= 400) {
        stats.errors++;
      }
      
      // Log endpoint statistics every 100 requests
      if (stats.count % 100 === 0) {
        logger.performance('Endpoint statistics', stats.totalDuration / stats.count, {
          endpoint,
          stats: {
            ...stats,
            avgDuration: stats.totalDuration / stats.count,
            errorRate: (stats.errors / stats.count) * 100
          }
        });
      }
    });
    
    next();
  };
};

// Memory usage monitoring
const memoryMonitor = () => {
  const checkMemory = () => {
    const usage = process.memoryUsage();
    const heapUsedMB = usage.heapUsed / 1024 / 1024;
    const heapTotalMB = usage.heapTotal / 1024 / 1024;
    const rssMB = usage.rss / 1024 / 1024;
    
    // Log if memory usage is high
    if (heapUsedMB > 500) { // 500MB threshold
      logger.performance('High memory usage detected', heapUsedMB, {
        memory: {
          heapUsed: `${heapUsedMB.toFixed(2)}MB`,
          heapTotal: `${heapTotalMB.toFixed(2)}MB`,
          rss: `${rssMB.toFixed(2)}MB`,
          external: `${(usage.external / 1024 / 1024).toFixed(2)}MB`
        }
      });
    }
    
    // Force garbage collection if available and memory is very high
    if (global.gc && heapUsedMB > 800) {
      logger.warn('Forcing garbage collection due to high memory usage');
      global.gc();
    }
  };
  
  // Check memory every 30 seconds
  setInterval(checkMemory, 30000);
  
  return checkMemory;
};

// CPU usage monitoring
const cpuMonitor = () => {
  let lastCpuUsage = process.cpuUsage();
  
  const checkCpu = () => {
    const currentCpuUsage = process.cpuUsage(lastCpuUsage);
    const userCpuTime = currentCpuUsage.user / 1000000; // Convert to seconds
    const systemCpuTime = currentCpuUsage.system / 1000000;
    const totalCpuTime = userCpuTime + systemCpuTime;
    
    // Log if CPU usage is high (over 5 seconds in the last 30 seconds)
    if (totalCpuTime > 5) {
      logger.performance('High CPU usage detected', totalCpuTime, {
        cpu: {
          user: `${userCpuTime.toFixed(2)}s`,
          system: `${systemCpuTime.toFixed(2)}s`,
          total: `${totalCpuTime.toFixed(2)}s`
        }
      });
    }
    
    lastCpuUsage = process.cpuUsage();
  };
  
  // Check CPU every 30 seconds
  setInterval(checkCpu, 30000);
  
  return checkCpu;
};

// Initialize monitoring
const initializeMonitoring = () => {
  memoryMonitor();
  cpuMonitor();
  
  logger.info('Performance monitoring initialized');
};

module.exports = {
  performanceMonitor,
  dbPerformanceMonitor,
  endpointPerformanceTracker,
  memoryMonitor,
  cpuMonitor,
  initializeMonitoring
};

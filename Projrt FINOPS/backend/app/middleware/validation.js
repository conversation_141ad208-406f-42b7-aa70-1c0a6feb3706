const { body, param, validationResult } = require('express-validator');

// Validation middleware
const validate = (req, res, next) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({
      success: false,
      message: 'Validation failed',
      errors: errors.array()
    });
  }
  next();
};

// Sanitize input to prevent XSS and injection attacks
const sanitizeInput = (value) => {
  if (typeof value !== 'string') return value;

  // Remove potentially dangerous characters
  return value
    .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '') // Remove script tags
    .replace(/javascript:/gi, '') // Remove javascript: protocol
    .replace(/on\w+\s*=/gi, '') // Remove event handlers
    .trim();
};

// Todo validation rules
const todoValidation = {
  create: [
    body('title')
      .trim()
      .notEmpty()
      .withMessage('Title is required')
      .isLength({ min: 1, max: 100 })
      .withMessage('Title must be between 1 and 100 characters')
      .escape(),
    body('description')
      .optional()
      .trim()
      .isLength({ max: 500 })
      .withMessage('Description must not exceed 500 characters')
      .escape(),
    body('completed')
      .optional()
      .isBoolean()
      .withMessage('Completed must be a boolean'),
    validate
  ],
  
  update: [
    param('id')
      .isMongoId()
      .withMessage('Invalid todo ID'),
    body('title')
      .optional()
      .trim()
      .notEmpty()
      .withMessage('Title cannot be empty')
      .isLength({ min: 1, max: 100 })
      .withMessage('Title must be between 1 and 100 characters')
      .escape(),
    body('description')
      .optional()
      .trim()
      .isLength({ max: 500 })
      .withMessage('Description must not exceed 500 characters')
      .escape(),
    body('completed')
      .optional()
      .isBoolean()
      .withMessage('Completed must be a boolean'),
    validate
  ],
  
  getId: [
    param('id')
      .isMongoId()
      .withMessage('Invalid todo ID'),
    validate
  ]
};

// Authentication validation rules
const authValidation = {
  register: [
    body('username')
      .trim()
      .isLength({ min: 3, max: 30 })
      .withMessage('Username must be between 3 and 30 characters')
      .matches(/^[a-zA-Z0-9_]+$/)
      .withMessage('Username can only contain letters, numbers, and underscores')
      .customSanitizer(sanitizeInput),
    body('email')
      .trim()
      .isEmail()
      .withMessage('Please enter a valid email')
      .normalizeEmail()
      .customSanitizer(sanitizeInput),
    body('password')
      .isLength({ min: 8 })
      .withMessage('Password must be at least 8 characters long')
      .matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/)
      .withMessage('Password must contain at least one uppercase letter, one lowercase letter, one number, and one special character'),
    body('firstName')
      .trim()
      .isLength({ min: 1, max: 50 })
      .withMessage('First name must be between 1 and 50 characters')
      .customSanitizer(sanitizeInput),
    body('lastName')
      .trim()
      .isLength({ min: 1, max: 50 })
      .withMessage('Last name must be between 1 and 50 characters')
      .customSanitizer(sanitizeInput),
    body('role')
      .optional()
      .isIn(['user', 'admin', 'finops_manager'])
      .withMessage('Invalid role'),
    validate
  ],

  login: [
    body('identifier')
      .trim()
      .notEmpty()
      .withMessage('Email or username is required')
      .customSanitizer(sanitizeInput),
    body('password')
      .notEmpty()
      .withMessage('Password is required'),
    validate
  ],

  refreshToken: [
    body('refreshToken')
      .notEmpty()
      .withMessage('Refresh token is required'),
    validate
  ]
};

module.exports = { todoValidation, authValidation, validate, sanitizeInput };
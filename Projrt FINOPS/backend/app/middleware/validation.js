const { body, param, validationResult } = require('express-validator');

// Validation middleware
const validate = (req, res, next) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({
      success: false,
      message: 'Validation failed',
      errors: errors.array()
    });
  }
  next();
};

// Todo validation rules
const todoValidation = {
  create: [
    body('title')
      .trim()
      .notEmpty()
      .withMessage('Title is required')
      .isLength({ min: 1, max: 100 })
      .withMessage('Title must be between 1 and 100 characters')
      .escape(),
    body('description')
      .optional()
      .trim()
      .isLength({ max: 500 })
      .withMessage('Description must not exceed 500 characters')
      .escape(),
    body('completed')
      .optional()
      .isBoolean()
      .withMessage('Completed must be a boolean'),
    validate
  ],
  
  update: [
    param('id')
      .isMongoId()
      .withMessage('Invalid todo ID'),
    body('title')
      .optional()
      .trim()
      .notEmpty()
      .withMessage('Title cannot be empty')
      .isLength({ min: 1, max: 100 })
      .withMessage('Title must be between 1 and 100 characters')
      .escape(),
    body('description')
      .optional()
      .trim()
      .isLength({ max: 500 })
      .withMessage('Description must not exceed 500 characters')
      .escape(),
    body('completed')
      .optional()
      .isBoolean()
      .withMessage('Completed must be a boolean'),
    validate
  ],
  
  getId: [
    param('id')
      .isMongoId()
      .withMessage('Invalid todo ID'),
    validate
  ]
};

module.exports = { todoValidation, validate };
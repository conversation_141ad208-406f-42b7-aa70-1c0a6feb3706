const mongoose = require('mongoose');

const resourceSchema = new mongoose.Schema({
  name: {
    type: String,
    required: [true, 'Resource name is required'],
    trim: true,
    maxlength: [100, 'Resource name cannot exceed 100 characters']
  },
  type: {
    type: String,
    required: [true, 'Resource type is required'],
    enum: ['pod', 'service', 'deployment', 'statefulset', 'daemonset', 'job', 'cronjob', 'pvc', 'node'],
    index: true
  },
  namespace: {
    type: String,
    required: [true, 'Namespace is required'],
    trim: true,
    index: true
  },
  cluster: {
    type: String,
    required: [true, 'Cluster name is required'],
    trim: true,
    default: 'default',
    index: true
  },
  labels: {
    type: Map,
    of: String,
    default: new Map()
  },
  annotations: {
    type: Map,
    of: String,
    default: new Map()
  },
  specifications: {
    cpu: {
      requests: { type: String },
      limits: { type: String }
    },
    memory: {
      requests: { type: String },
      limits: { type: String }
    },
    storage: {
      requests: { type: String },
      class: { type: String }
    },
    replicas: { type: Number, default: 1 }
  },
  status: {
    type: String,
    enum: ['running', 'pending', 'failed', 'succeeded', 'unknown'],
    default: 'unknown',
    index: true
  },
  owner: {
    userId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User',
      index: true
    },
    team: {
      type: String,
      trim: true,
      index: true
    },
    project: {
      type: String,
      trim: true,
      index: true
    },
    environment: {
      type: String,
      enum: ['development', 'staging', 'production', 'testing'],
      default: 'development',
      index: true
    }
  },
  costCenter: {
    type: String,
    trim: true,
    index: true
  },
  tags: [{
    key: { type: String, required: true },
    value: { type: String, required: true }
  }],
  isActive: {
    type: Boolean,
    default: true,
    index: true
  },
  createdAt: {
    type: Date,
    default: Date.now,
    index: true
  },
  updatedAt: {
    type: Date,
    default: Date.now
  },
  deletedAt: {
    type: Date,
    index: true
  }
});

// Indexes for performance
resourceSchema.index({ cluster: 1, namespace: 1, type: 1 });
resourceSchema.index({ 'owner.team': 1, 'owner.environment': 1 });
resourceSchema.index({ costCenter: 1, createdAt: -1 });
resourceSchema.index({ isActive: 1, status: 1 });

// Pre-save middleware
resourceSchema.pre('save', function(next) {
  this.updatedAt = new Date();
  next();
});

// Virtual for resource identifier
resourceSchema.virtual('identifier').get(function() {
  return `${this.cluster}/${this.namespace}/${this.type}/${this.name}`;
});

// Static methods
resourceSchema.statics.getResourcesByTeam = function(team, environment = null) {
  const query = { 'owner.team': team, isActive: true };
  if (environment) {
    query['owner.environment'] = environment;
  }
  return this.find(query).sort({ createdAt: -1 });
};

resourceSchema.statics.getResourcesByProject = function(project, environment = null) {
  const query = { 'owner.project': project, isActive: true };
  if (environment) {
    query['owner.environment'] = environment;
  }
  return this.find(query).sort({ createdAt: -1 });
};

resourceSchema.statics.getResourceStats = function() {
  return this.aggregate([
    { $match: { isActive: true } },
    {
      $group: {
        _id: {
          type: '$type',
          environment: '$owner.environment',
          status: '$status'
        },
        count: { $sum: 1 },
        totalReplicas: { $sum: '$specifications.replicas' }
      }
    },
    {
      $group: {
        _id: {
          type: '$_id.type',
          environment: '$_id.environment'
        },
        statusBreakdown: {
          $push: {
            status: '$_id.status',
            count: '$count',
            totalReplicas: '$totalReplicas'
          }
        },
        totalCount: { $sum: '$count' },
        totalReplicas: { $sum: '$totalReplicas' }
      }
    }
  ]);
};

// Soft delete method
resourceSchema.methods.softDelete = function() {
  this.isActive = false;
  this.deletedAt = new Date();
  return this.save();
};

module.exports = mongoose.model('Resource', resourceSchema);

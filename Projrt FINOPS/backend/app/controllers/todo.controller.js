const Todo = require('../models/todo.model.js');

// Créer et sauvegarder un nouveau todo
exports.create = (req, res) => {
  // Validation
  if (!req.body.title) {
    return res.status(400).json({
      success: false,
      message: 'Le titre ne peut pas être vide!'
    });
  }

  // Créer un todo avec l'ID de l'utilisateur
  const todo = new Todo({
    title: req.body.title,
    description: req.body.description || '',
    completed: req.body.completed || false,
    userId: req.user.userId // Add user ID from authenticated user
  });

  // Sauvegarder en base
  todo
    .save()
    .then(data => {
      res.json({
        success: true,
        message: 'Todo créé avec succès',
        data
      });
    })
    .catch(err => {
      console.error('Error creating todo:', err);
      res.status(500).json({
        success: false,
        message: err.message || 'Erreur lors de la création du todo.',
        error: process.env.NODE_ENV === 'development' ? err.message : 'Internal server error'
      });
    });
};

// Récupérer tous les todos (user-specific or admin sees all)
exports.findAll = (req, res) => {
  const title = req.query.title;
  let condition = {};

  // If user is not admin, only show their todos
  if (req.user.role !== 'admin') {
    condition.userId = req.user.userId;
  }

  // Add title filter if provided
  if (title) {
    condition.title = { $regex: new RegExp(title), $options: 'i' };
  }

  Todo.find(condition)
    .sort({ createdAt: -1 })
    .populate('userId', 'username firstName lastName') // Populate user info
    .then(data => {
      res.json({
        success: true,
        count: data.length,
        data
      });
    })
    .catch(err => {
      console.error('Error fetching todos:', err);
      res.status(500).json({
        success: false,
        message: err.message || 'Erreur lors de la récupération des todos.',
        error: process.env.NODE_ENV === 'development' ? err.message : 'Internal server error'
      });
    });
};

// Trouver un todo par ID
exports.findOne = (req, res) => {
  const id = req.params.id;

  Todo.findById(id)
    .then(data => {
      if (!data) {
        res.status(404).send({ message: 'Todo non trouvé avec l\'id ' + id });
      } else {
        res.send(data);
      }
    })
    .catch(err => {
      res.status(500).send({ message: 'Erreur lors de la récupération du todo avec l\'id=' + id });
    });
};

// Mettre à jour un todo par ID
exports.update = (req, res) => {
  if (!req.body) {
    return res.status(400).send({
      message: 'Les données à mettre à jour ne peuvent pas être vides!'
    });
  }

  const id = req.params.id;

  Todo.findByIdAndUpdate(id, req.body, { useFindAndModify: false, new: true })
    .then(data => {
      if (!data) {
        res.status(404).send({
          message: `Impossible de mettre à jour le todo avec l'id=${id}. Todo non trouvé!`
        });
      } else {
        res.send(data);
      }
    })
    .catch(err => {
      res.status(500).send({
        message: 'Erreur lors de la mise à jour du todo avec l\'id=' + id
      });
    });
};

// Supprimer un todo par ID
exports.delete = (req, res) => {
  const id = req.params.id;

  Todo.findByIdAndRemove(id)
    .then(data => {
      if (!data) {
        res.status(404).send({
          message: `Impossible de supprimer le todo avec l'id=${id}. Todo non trouvé!`
        });
      } else {
        res.send({
          message: 'Todo supprimé avec succès!'
        });
      }
    })
    .catch(err => {
      res.status(500).send({
        message: 'Impossible de supprimer le todo avec l\'id=' + id
      });
    });
};

// Supprimer tous les todos
exports.deleteAll = (req, res) => {
  Todo.deleteMany({})
    .then(data => {
      res.send({
        message: `${data.deletedCount} todos ont été supprimés avec succès!`
      });
    })
    .catch(err => {
      res.status(500).send({
        message: err.message || 'Erreur lors de la suppression des todos.'
      });
    });
};

// Trouver tous les todos complétés
exports.findAllCompleted = (req, res) => {
  Todo.find({ completed: true })
    .sort({ createdAt: -1 })
    .then(data => {
      res.send(data);
    })
    .catch(err => {
      res.status(500).send({
        message: err.message || 'Erreur lors de la récupération des todos complétés.'
      });
    });
};

// Rechercher des todos par titre
exports.findByTitle = (req, res) => {
  const title = req.params.title;
  
  Todo.find({ title: { $regex: new RegExp(title), $options: 'i' } })
    .sort({ createdAt: -1 })
    .then(data => {
      res.send(data);
    })
    .catch(err => {
      res.status(500).send({
        message: err.message || 'Erreur lors de la recherche des todos.'
      });
    });
};

// Obtenir des statistiques sur les todos
exports.getStats = (req, res) => {
  Promise.all([
    Todo.countDocuments(),
    Todo.countDocuments({ completed: true }),
    Todo.countDocuments({ completed: false }),
    Todo.aggregate([
      {
        $group: {
          _id: null,
          avgTitleLength: { $avg: { $strLenCP: '$title' } },
          oldestTodo: { $min: '$createdAt' },
          newestTodo: { $max: '$createdAt' }
        }
      }
    ])
  ])
  .then(([total, completed, pending, aggregates]) => {
    const stats = aggregates[0] || {};
    res.send({
      total,
      completed,
      pending,
      completionRate: total > 0 ? (completed / total * 100).toFixed(2) : 0,
      avgTitleLength: stats.avgTitleLength || 0,
      oldestTodo: stats.oldestTodo,
      newestTodo: stats.newestTodo
    });
  })
  .catch(err => {
    res.status(500).send({
      message: err.message || 'Erreur lors de la récupération des statistiques.'
    });
  });
};

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>FinOps Backend Connectivity Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-result { margin: 10px 0; padding: 10px; border-radius: 5px; }
        .success { background-color: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background-color: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .loading { background-color: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
        button { padding: 10px 20px; margin: 5px; background-color: #007bff; color: white; border: none; border-radius: 5px; cursor: pointer; }
        button:hover { background-color: #0056b3; }
        pre { background-color: #f8f9fa; padding: 10px; border-radius: 5px; overflow-x: auto; }
    </style>
</head>
<body>
    <h1>🔧 FinOps Backend Connectivity Test</h1>
    
    <div id="results"></div>
    
    <button onclick="testBackendHealth()">Test Backend Health</button>
    <button onclick="testAuthentication()">Test Authentication</button>
    <button onclick="testFinOpsDashboard()">Test FinOps Dashboard</button>
    <button onclick="testAllEndpoints()">Test All Endpoints</button>
    
    <script>
        const API_BASE = 'http://localhost:8080';
        let authToken = null;
        
        function addResult(message, type = 'loading') {
            const results = document.getElementById('results');
            const div = document.createElement('div');
            div.className = `test-result ${type}`;
            div.innerHTML = message;
            results.appendChild(div);
            return div;
        }
        
        function updateResult(element, message, type) {
            element.className = `test-result ${type}`;
            element.innerHTML = message;
        }
        
        async function testBackendHealth() {
            const result = addResult('🔄 Testing backend health...', 'loading');
            
            try {
                const response = await fetch(`${API_BASE}/health`);
                const data = await response.json();
                
                if (response.ok) {
                    updateResult(result, `✅ Backend Health: OK<br><pre>${JSON.stringify(data, null, 2)}</pre>`, 'success');
                } else {
                    updateResult(result, `❌ Backend Health: Failed (${response.status})`, 'error');
                }
            } catch (error) {
                updateResult(result, `❌ Backend Health: Connection failed - ${error.message}`, 'error');
            }
        }
        
        async function testAuthentication() {
            const result = addResult('🔄 Testing authentication...', 'loading');
            
            try {
                // Try to login with existing user
                const loginResponse = await fetch(`${API_BASE}/api/auth/login`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        identifier: '<EMAIL>',
                        password: 'FinOpsAdmin123!'
                    })
                });
                
                const loginData = await loginResponse.json();
                
                if (loginResponse.ok && loginData.token) {
                    authToken = loginData.token;
                    updateResult(result, `✅ Authentication: Success<br>Token: ${authToken.substring(0, 50)}...`, 'success');
                } else {
                    updateResult(result, `❌ Authentication: Login failed<br><pre>${JSON.stringify(loginData, null, 2)}</pre>`, 'error');
                }
            } catch (error) {
                updateResult(result, `❌ Authentication: Failed - ${error.message}`, 'error');
            }
        }
        
        async function testFinOpsDashboard() {
            const result = addResult('🔄 Testing FinOps Dashboard...', 'loading');
            
            if (!authToken) {
                updateResult(result, '⚠️ Please run authentication test first to get token', 'error');
                return;
            }
            
            try {
                const response = await fetch(`${API_BASE}/api/finops/dashboard`, {
                    headers: {
                        'Authorization': `Bearer ${authToken}`,
                        'Content-Type': 'application/json'
                    }
                });
                
                const data = await response.json();
                
                if (response.ok) {
                    updateResult(result, `✅ FinOps Dashboard: Success<br><pre>${JSON.stringify(data, null, 2).substring(0, 500)}...</pre>`, 'success');
                } else {
                    updateResult(result, `❌ FinOps Dashboard: Failed (${response.status})<br><pre>${JSON.stringify(data, null, 2)}</pre>`, 'error');
                }
            } catch (error) {
                updateResult(result, `❌ FinOps Dashboard: Failed - ${error.message}`, 'error');
            }
        }
        
        async function testAllEndpoints() {
            await testBackendHealth();
            await new Promise(resolve => setTimeout(resolve, 1000));
            await testAuthentication();
            await new Promise(resolve => setTimeout(resolve, 1000));
            await testFinOpsDashboard();
        }
        
        async function testCompleteWorkflow() {
            await testBackendHealth();
            await new Promise(resolve => setTimeout(resolve, 1000));
            await testAuthentication();
            await new Promise(resolve => setTimeout(resolve, 1000));
            await testFinOpsDashboard();

            // Test additional endpoints
            if (authToken) {
                await testCostAnalysis();
                await testBudgetManagement();
                await testReports();
            }
        }

        async function testCostAnalysis() {
            const result = addResult('🔄 Testing Cost Analysis...', 'loading');

            try {
                const response = await fetch(`${API_BASE}/api/finops/cost-analysis`, {
                    headers: {
                        'Authorization': `Bearer ${authToken}`,
                        'Content-Type': 'application/json'
                    }
                });

                const data = await response.json();

                if (response.ok) {
                    updateResult(result, `✅ Cost Analysis: Success<br><pre>${JSON.stringify(data, null, 2).substring(0, 300)}...</pre>`, 'success');
                } else {
                    updateResult(result, `❌ Cost Analysis: Failed (${response.status})`, 'error');
                }
            } catch (error) {
                updateResult(result, `❌ Cost Analysis: Failed - ${error.message}`, 'error');
            }
        }

        async function testBudgetManagement() {
            const result = addResult('🔄 Testing Budget Management...', 'loading');

            try {
                const response = await fetch(`${API_BASE}/api/finops/budgets`, {
                    headers: {
                        'Authorization': `Bearer ${authToken}`,
                        'Content-Type': 'application/json'
                    }
                });

                const data = await response.json();

                if (response.ok) {
                    updateResult(result, `✅ Budget Management: Success<br>Found ${data.budgets?.length || 0} budgets`, 'success');
                } else {
                    updateResult(result, `❌ Budget Management: Failed (${response.status})`, 'error');
                }
            } catch (error) {
                updateResult(result, `❌ Budget Management: Failed - ${error.message}`, 'error');
            }
        }

        async function testReports() {
            const result = addResult('🔄 Testing Reports...', 'loading');

            try {
                const response = await fetch(`${API_BASE}/api/finops/cost-report`, {
                    headers: {
                        'Authorization': `Bearer ${authToken}`,
                        'Content-Type': 'application/json'
                    }
                });

                const data = await response.json();

                if (response.ok) {
                    updateResult(result, `✅ Reports: Success<br>Report generated successfully`, 'success');
                } else {
                    updateResult(result, `❌ Reports: Failed (${response.status})`, 'error');
                }
            } catch (error) {
                updateResult(result, `❌ Reports: Failed - ${error.message}`, 'error');
            }
        }

        // Auto-run complete workflow test on page load
        window.onload = function() {
            testCompleteWorkflow();
        };
    </script>
</body>
</html>

groups:
- name: finops-application
  rules:
  # High Error Rate
  - alert: HighErrorRate
    expr: rate(http_requests_total{status=~"5.."}[5m]) > 0.1
    for: 5m
    labels:
      severity: critical
      service: finops-backend
    annotations:
      summary: "High error rate detected"
      description: "Error rate is {{ $value }} errors per second for {{ $labels.instance }}"

  # High Response Time
  - alert: HighResponseTime
    expr: histogram_quantile(0.95, rate(http_request_duration_seconds_bucket[5m])) > 2
    for: 5m
    labels:
      severity: warning
      service: finops-backend
    annotations:
      summary: "High response time detected"
      description: "95th percentile response time is {{ $value }}s for {{ $labels.instance }}"

  # High Memory Usage
  - alert: HighMemoryUsage
    expr: (process_resident_memory_bytes / 1024 / 1024) > 800
    for: 10m
    labels:
      severity: warning
      service: finops-backend
    annotations:
      summary: "High memory usage detected"
      description: "Memory usage is {{ $value }}MB for {{ $labels.instance }}"

  # High CPU Usage
  - alert: HighCPUUsage
    expr: rate(process_cpu_seconds_total[5m]) * 100 > 80
    for: 10m
    labels:
      severity: warning
      service: finops-backend
    annotations:
      summary: "High CPU usage detected"
      description: "CPU usage is {{ $value }}% for {{ $labels.instance }}"

  # Service Down
  - alert: ServiceDown
    expr: up == 0
    for: 1m
    labels:
      severity: critical
    annotations:
      summary: "Service is down"
      description: "{{ $labels.job }} service is down for {{ $labels.instance }}"

  # Database Connection Issues
  - alert: DatabaseConnectionIssues
    expr: mongodb_connections_current / mongodb_connections_available > 0.8
    for: 5m
    labels:
      severity: warning
      service: mongodb
    annotations:
      summary: "High database connection usage"
      description: "Database connection usage is {{ $value | humanizePercentage }} for {{ $labels.instance }}"

  # Disk Space Low
  - alert: DiskSpaceLow
    expr: (node_filesystem_avail_bytes / node_filesystem_size_bytes) * 100 < 10
    for: 5m
    labels:
      severity: critical
    annotations:
      summary: "Low disk space"
      description: "Disk space is {{ $value | humanizePercentage }} available for {{ $labels.instance }}"

- name: finops-business-metrics
  rules:
  # High Cost Alert
  - alert: HighCostDetected
    expr: finops_total_cost_euros > 10000
    for: 1h
    labels:
      severity: warning
      team: finops
    annotations:
      summary: "High cost detected"
      description: "Total cost is €{{ $value }} which exceeds the threshold"

  # Budget Exceeded
  - alert: BudgetExceeded
    expr: finops_budget_utilization_percent > 100
    for: 5m
    labels:
      severity: critical
      team: finops
    annotations:
      summary: "Budget exceeded"
      description: "Budget {{ $labels.budget_name }} is {{ $value }}% utilized"

  # Budget Warning
  - alert: BudgetWarning
    expr: finops_budget_utilization_percent > 80
    for: 15m
    labels:
      severity: warning
      team: finops
    annotations:
      summary: "Budget warning"
      description: "Budget {{ $labels.budget_name }} is {{ $value }}% utilized"

  # Unusual Cost Spike
  - alert: CostSpike
    expr: increase(finops_total_cost_euros[1h]) > increase(finops_total_cost_euros[1h] offset 24h) * 2
    for: 30m
    labels:
      severity: warning
      team: finops
    annotations:
      summary: "Unusual cost spike detected"
      description: "Cost increase in the last hour is significantly higher than usual"

  # Resource Optimization Opportunity
  - alert: OptimizationOpportunity
    expr: finops_optimization_savings_potential_euros > 1000
    for: 1h
    labels:
      severity: info
      team: finops
    annotations:
      summary: "Optimization opportunity detected"
      description: "Potential savings of €{{ $value }} identified"

- name: kubernetes-resources
  rules:
  # Pod Restart Rate
  - alert: PodRestartRate
    expr: rate(kube_pod_container_status_restarts_total[15m]) > 0
    for: 5m
    labels:
      severity: warning
    annotations:
      summary: "Pod restarting frequently"
      description: "Pod {{ $labels.pod }} in namespace {{ $labels.namespace }} is restarting frequently"

  # Pod Memory Usage
  - alert: PodMemoryUsage
    expr: container_memory_usage_bytes / container_spec_memory_limit_bytes > 0.9
    for: 10m
    labels:
      severity: warning
    annotations:
      summary: "Pod memory usage high"
      description: "Pod {{ $labels.pod }} memory usage is {{ $value | humanizePercentage }}"

  # Pod CPU Usage
  - alert: PodCPUUsage
    expr: rate(container_cpu_usage_seconds_total[5m]) / container_spec_cpu_quota * container_spec_cpu_period > 0.9
    for: 10m
    labels:
      severity: warning
    annotations:
      summary: "Pod CPU usage high"
      description: "Pod {{ $labels.pod }} CPU usage is {{ $value | humanizePercentage }}"

  # Deployment Replica Mismatch
  - alert: DeploymentReplicaMismatch
    expr: kube_deployment_spec_replicas != kube_deployment_status_replicas_available
    for: 5m
    labels:
      severity: warning
    annotations:
      summary: "Deployment replica mismatch"
      description: "Deployment {{ $labels.deployment }} has {{ $labels.spec_replicas }} desired but {{ $labels.available_replicas }} available"

- name: security-alerts
  rules:
  # High Failed Login Rate
  - alert: HighFailedLoginRate
    expr: rate(finops_auth_failed_attempts_total[5m]) > 5
    for: 2m
    labels:
      severity: warning
      security: true
    annotations:
      summary: "High failed login rate detected"
      description: "Failed login rate is {{ $value }} attempts per second"

  # Suspicious API Activity
  - alert: SuspiciousAPIActivity
    expr: rate(http_requests_total{status="401"}[5m]) > 10
    for: 5m
    labels:
      severity: warning
      security: true
    annotations:
      summary: "Suspicious API activity detected"
      description: "High rate of 401 responses: {{ $value }} per second"

  # Rate Limit Triggered
  - alert: RateLimitTriggered
    expr: rate(finops_rate_limit_triggered_total[5m]) > 1
    for: 1m
    labels:
      severity: info
      security: true
    annotations:
      summary: "Rate limiting triggered"
      description: "Rate limiting is being triggered {{ $value }} times per second"

# Production Environment Configuration
# =====================================

# Application Environment
NODE_ENV=production
PORT=8080
FRONTEND_PORT=3000

# Database Configuration
DB_URL=mongodb://mongodb:27017/finops_production
DB_MAX_POOL_SIZE=20
DB_MIN_POOL_SIZE=5
DB_MAX_IDLE_TIME=30000
DB_SERVER_SELECTION_TIMEOUT=30000
DB_SOCKET_TIMEOUT=45000
DB_CONNECT_TIMEOUT=30000
DB_HEARTBEAT_FREQUENCY=10000
DB_WRITE_TIMEOUT=10000
DB_SSL=true

# JWT Configuration
JWT_SECRET=your-super-secure-jwt-secret-key-change-in-production
JWT_EXPIRES_IN=24h
JWT_REFRESH_EXPIRES_IN=7d
JWT_ISSUER=finops-app
JWT_AUDIENCE=finops-users

# Security Configuration
BCRYPT_ROUNDS=12
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100
RATE_LIMIT_AUTH_MAX_REQUESTS=5

# CORS Configuration
CORS_ORIGIN=https://your-domain.com,https://www.your-domain.com
CORS_CREDENTIALS=true

# Logging Configuration
LOG_LEVEL=info
LOG_FILE_PATH=/app/logs/app.log
LOG_MAX_SIZE=10m
LOG_MAX_FILES=5
LOG_DATE_PATTERN=YYYY-MM-DD

# Performance Monitoring
PERFORMANCE_MONITORING=true
MEMORY_THRESHOLD_MB=500
CPU_THRESHOLD_SECONDS=5

# Email Configuration (for notifications)
SMTP_HOST=smtp.your-email-provider.com
SMTP_PORT=587
SMTP_SECURE=false
SMTP_USER=<EMAIL>
SMTP_PASS=your-email-password
EMAIL_FROM=<EMAIL>

# Redis Configuration (for session storage)
REDIS_URL=redis://redis:6379
REDIS_PASSWORD=your-redis-password

# Monitoring and Health Checks
HEALTH_CHECK_INTERVAL=30000
METRICS_ENABLED=true
PROMETHEUS_PORT=9090

# File Upload Configuration
MAX_FILE_SIZE=10mb
UPLOAD_PATH=/app/uploads
ALLOWED_FILE_TYPES=csv,json,xlsx

# API Configuration
API_VERSION=v1
API_RATE_LIMIT=1000
API_TIMEOUT=30000

# Feature Flags
ENABLE_BUDGET_ALERTS=true
ENABLE_COST_OPTIMIZATION=true
ENABLE_EXPORT_FEATURES=true
ENABLE_ADVANCED_ANALYTICS=true

# External Services
KUBERNETES_API_URL=https://kubernetes.default.svc
PROMETHEUS_URL=http://prometheus:9090
GRAFANA_URL=http://grafana:3000

# Backup Configuration
BACKUP_ENABLED=true
BACKUP_SCHEDULE=0 2 * * *
BACKUP_RETENTION_DAYS=30
BACKUP_S3_BUCKET=finops-backups

# SSL/TLS Configuration
SSL_ENABLED=true
SSL_CERT_PATH=/app/certs/cert.pem
SSL_KEY_PATH=/app/certs/key.pem

# Cache Configuration
CACHE_TTL=3600
CACHE_MAX_SIZE=100mb

# Timezone
TZ=UTC

apiVersion: v1
kind: Namespace
metadata:
  name: finops-production
  labels:
    name: finops-production
    environment: production
    app: finops
  annotations:
    description: "FinOps application production environment"
---
apiVersion: v1
kind: ResourceQuota
metadata:
  name: finops-production-quota
  namespace: finops-production
spec:
  hard:
    requests.cpu: "4"
    requests.memory: 8Gi
    limits.cpu: "8"
    limits.memory: 16Gi
    persistentvolumeclaims: "10"
    services: "10"
    secrets: "20"
    configmaps: "20"
---
apiVersion: v1
kind: LimitRange
metadata:
  name: finops-production-limits
  namespace: finops-production
spec:
  limits:
  - default:
      cpu: "1"
      memory: "1Gi"
    defaultRequest:
      cpu: "100m"
      memory: "128Mi"
    type: Container
  - default:
      storage: "10Gi"
    type: PersistentVolumeClaim

apiVersion: v1
kind: Secret
metadata:
  name: mongodb-secret
  namespace: finops-production
type: Opaque
data:
  mongodb-root-username: YWRtaW4=  # admin
  mongodb-root-password: c2VjdXJlX3Byb2R1Y3Rpb25fcGFzc3dvcmQ=  # secure_production_password
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: mongodb-config
  namespace: finops-production
data:
  mongodb.conf: |
    storage:
      dbPath: /data/db
      journal:
        enabled: true
    systemLog:
      destination: file
      logAppend: true
      path: /var/log/mongodb/mongod.log
    net:
      port: 27017
      bindIp: 0.0.0.0
    security:
      authorization: enabled
    replication:
      replSetName: rs0
---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: mongodb-pvc
  namespace: finops-production
spec:
  accessModes:
    - ReadWriteOnce
  resources:
    requests:
      storage: 50Gi
  storageClassName: fast-ssd
---
apiVersion: apps/v1
kind: StatefulSet
metadata:
  name: mongodb
  namespace: finops-production
  labels:
    app: mongodb
    component: database
spec:
  serviceName: mongodb-headless
  replicas: 1
  selector:
    matchLabels:
      app: mongodb
  template:
    metadata:
      labels:
        app: mongodb
        component: database
    spec:
      securityContext:
        fsGroup: 999
      containers:
      - name: mongodb
        image: mongo:5.0-focal
        ports:
        - containerPort: 27017
          name: mongodb
        env:
        - name: MONGO_INITDB_ROOT_USERNAME
          valueFrom:
            secretKeyRef:
              name: mongodb-secret
              key: mongodb-root-username
        - name: MONGO_INITDB_ROOT_PASSWORD
          valueFrom:
            secretKeyRef:
              name: mongodb-secret
              key: mongodb-root-password
        - name: MONGO_INITDB_DATABASE
          value: finops_production
        volumeMounts:
        - name: mongodb-storage
          mountPath: /data/db
        - name: mongodb-config
          mountPath: /etc/mongod.conf
          subPath: mongodb.conf
        resources:
          requests:
            cpu: 500m
            memory: 1Gi
          limits:
            cpu: 2
            memory: 4Gi
        livenessProbe:
          exec:
            command:
            - mongo
            - --eval
            - "db.adminCommand('ping')"
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
        readinessProbe:
          exec:
            command:
            - mongo
            - --eval
            - "db.adminCommand('ping')"
          initialDelaySeconds: 5
          periodSeconds: 5
          timeoutSeconds: 3
          failureThreshold: 3
      volumes:
      - name: mongodb-storage
        persistentVolumeClaim:
          claimName: mongodb-pvc
      - name: mongodb-config
        configMap:
          name: mongodb-config
---
apiVersion: v1
kind: Service
metadata:
  name: mongodb
  namespace: finops-production
  labels:
    app: mongodb
spec:
  ports:
  - port: 27017
    targetPort: 27017
    name: mongodb
  selector:
    app: mongodb
  type: ClusterIP
---
apiVersion: v1
kind: Service
metadata:
  name: mongodb-headless
  namespace: finops-production
  labels:
    app: mongodb
spec:
  ports:
  - port: 27017
    targetPort: 27017
    name: mongodb
  selector:
    app: mongodb
  clusterIP: None

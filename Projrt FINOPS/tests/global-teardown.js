const fs = require('fs');
const path = require('path');

async function globalTeardown(config) {
  console.log('🧹 Starting global teardown for FinOps application tests...');
  
  try {
    // Clean up test data if needed
    await cleanupTestData();
    
    // Generate test summary report
    await generateTestSummary();
    
    // Clean up temporary files (but keep auth state for debugging)
    await cleanupTempFiles();
    
    console.log('✅ Global teardown completed successfully!');
  } catch (error) {
    console.error('❌ Global teardown failed:', error);
  }
}

async function cleanupTestData() {
  try {
    // Clean up test database if needed
    const apiUrl = 'http://localhost:8080/api';
    
    // You could add cleanup API calls here if needed
    // For now, we'll just log that cleanup is happening
    console.log('🗑️ Test data cleanup completed');
  } catch (error) {
    console.log('⚠️ Test data cleanup failed:', error.message);
  }
}

async function generateTestSummary() {
  try {
    const testResultsPath = path.join(__dirname, '../test-results/results.json');
    
    if (fs.existsSync(testResultsPath)) {
      const results = JSON.parse(fs.readFileSync(testResultsPath, 'utf8'));
      
      const summary = {
        timestamp: new Date().toISOString(),
        totalTests: results.stats?.total || 0,
        passed: results.stats?.passed || 0,
        failed: results.stats?.failed || 0,
        skipped: results.stats?.skipped || 0,
        duration: results.stats?.duration || 0,
        projects: results.suites?.map(suite => ({
          name: suite.title,
          tests: suite.specs?.length || 0,
          passed: suite.specs?.filter(spec => spec.ok).length || 0,
          failed: suite.specs?.filter(spec => !spec.ok).length || 0
        })) || []
      };
      
      // Write summary to file
      const summaryPath = path.join(__dirname, '../test-results/summary.json');
      fs.writeFileSync(summaryPath, JSON.stringify(summary, null, 2));
      
      // Log summary to console
      console.log('📊 Test Summary:');
      console.log(`   Total Tests: ${summary.totalTests}`);
      console.log(`   Passed: ${summary.passed}`);
      console.log(`   Failed: ${summary.failed}`);
      console.log(`   Skipped: ${summary.skipped}`);
      console.log(`   Duration: ${(summary.duration / 1000).toFixed(2)}s`);
      
      if (summary.failed > 0) {
        console.log('❌ Some tests failed. Check the detailed report for more information.');
      } else {
        console.log('✅ All tests passed successfully!');
      }
    }
  } catch (error) {
    console.log('⚠️ Failed to generate test summary:', error.message);
  }
}

async function cleanupTempFiles() {
  try {
    // Clean up temporary files but keep important ones
    const tempDirs = [
      path.join(__dirname, '../test-results/temp'),
      path.join(__dirname, '../playwright-report/temp')
    ];
    
    for (const dir of tempDirs) {
      if (fs.existsSync(dir)) {
        fs.rmSync(dir, { recursive: true, force: true });
        console.log(`🗑️ Cleaned up temporary directory: ${dir}`);
      }
    }
  } catch (error) {
    console.log('⚠️ Failed to cleanup temporary files:', error.message);
  }
}

module.exports = globalTeardown;

const { test, expect } = require('@playwright/test');

test.describe('FinOps Application - Comprehensive Testing', () => {
  const API_BASE = 'http://localhost:8080';
  let authToken = null;

  test.beforeAll(async () => {
    console.log('🚀 Starting comprehensive FinOps application testing...');
  });

  test('Backend Health Check', async ({ request }) => {
    console.log('🔍 Testing backend health endpoint...');
    
    const response = await request.get(`${API_BASE}/health`);
    expect(response.ok()).toBeTruthy();
    
    const health = await response.json();
    expect(health).toHaveProperty('status', 'healthy');
    expect(health).toHaveProperty('timestamp');
    expect(health).toHaveProperty('database');
    
    console.log('✅ Backend health check passed');
  });

  test('Database Connectivity', async ({ request }) => {
    console.log('🔍 Testing database connectivity...');
    
    const response = await request.get(`${API_BASE}/ready`);
    expect(response.ok()).toBeTruthy();
    
    const readiness = await response.json();
    expect(readiness).toHaveProperty('status', 'ready');
    
    console.log('✅ Database connectivity verified');
  });

  test('User Authentication Flow', async ({ request }) => {
    console.log('🔍 Testing user authentication...');
    
    // Test user registration
    const registerResponse = await request.post(`${API_BASE}/api/auth/register`, {
      data: {
        username: `testuser_${Date.now()}`,
        email: `test_${Date.now()}@finops.com`,
        password: 'TestPass123!',
        firstName: 'Test',
        lastName: 'User'
      }
    });
    
    expect([201, 409]).toContain(registerResponse.status()); // 201 for new user, 409 if exists
    
    // Test user login with existing user
    const loginResponse = await request.post(`${API_BASE}/api/auth/login`, {
      data: {
        identifier: '<EMAIL>',
        password: 'FinOpsAdmin123!'
      }
    });
    
    expect(loginResponse.ok()).toBeTruthy();
    const loginData = await loginResponse.json();
    expect(loginData).toHaveProperty('token');
    expect(loginData).toHaveProperty('user');
    
    authToken = loginData.token;
    console.log('✅ Authentication flow completed successfully');
  });

  test('FinOps Dashboard API', async ({ request }) => {
    console.log('🔍 Testing FinOps dashboard API...');
    
    expect(authToken).toBeTruthy();
    
    const response = await request.get(`${API_BASE}/api/finops/dashboard`, {
      headers: {
        'Authorization': `Bearer ${authToken}`
      }
    });
    
    expect(response.ok()).toBeTruthy();
    const dashboard = await response.json();
    
    expect(dashboard).toHaveProperty('summary');
    expect(dashboard).toHaveProperty('costTrends');
    expect(dashboard).toHaveProperty('teamBreakdown');
    expect(dashboard).toHaveProperty('topResources');
    expect(dashboard).toHaveProperty('userCosts');
    expect(dashboard).toHaveProperty('resourceSummary');
    expect(dashboard).toHaveProperty('budgets');
    
    console.log('✅ FinOps dashboard API working correctly');
  });

  test('Cost Analysis API', async ({ request }) => {
    console.log('🔍 Testing cost analysis API...');
    
    const response = await request.get(`${API_BASE}/api/finops/cost-analysis`, {
      headers: {
        'Authorization': `Bearer ${authToken}`
      }
    });
    
    expect(response.ok()).toBeTruthy();
    const costAnalysis = await response.json();
    
    expect(costAnalysis).toHaveProperty('dailyCosts');
    expect(Array.isArray(costAnalysis.dailyCosts)).toBeTruthy();
    
    console.log('✅ Cost analysis API working correctly');
  });

  test('Budget Management API', async ({ request }) => {
    console.log('🔍 Testing budget management API...');
    
    // Test getting budgets
    const getBudgetsResponse = await request.get(`${API_BASE}/api/finops/budgets`, {
      headers: {
        'Authorization': `Bearer ${authToken}`
      }
    });
    
    expect(getBudgetsResponse.ok()).toBeTruthy();
    const budgets = await getBudgetsResponse.json();
    expect(budgets).toHaveProperty('budgets');
    expect(Array.isArray(budgets.budgets)).toBeTruthy();
    
    // Test creating a budget
    const createBudgetResponse = await request.post(`${API_BASE}/api/finops/budgets`, {
      headers: {
        'Authorization': `Bearer ${authToken}`
      },
      data: {
        name: 'Test Budget E2E',
        description: 'End-to-end test budget',
        scope: {
          type: 'team',
          filters: {
            teams: ['e2e-test-team']
          }
        },
        period: {
          type: 'monthly',
          startDate: '2025-07-01T00:00:00.000Z',
          endDate: '2025-07-31T23:59:59.999Z'
        },
        amounts: {
          total: 2000,
          currency: 'EUR'
        }
      }
    });
    
    expect(createBudgetResponse.ok()).toBeTruthy();
    const newBudget = await createBudgetResponse.json();
    expect(newBudget).toHaveProperty('budget');
    expect(newBudget.budget).toHaveProperty('_id');
    
    console.log('✅ Budget management API working correctly');
  });

  test('Optimization Recommendations API', async ({ request }) => {
    console.log('🔍 Testing optimization recommendations API...');
    
    const response = await request.get(`${API_BASE}/api/finops/optimization-recommendations`, {
      headers: {
        'Authorization': `Bearer ${authToken}`
      }
    });
    
    expect(response.ok()).toBeTruthy();
    const recommendations = await response.json();
    
    expect(recommendations).toHaveProperty('recommendations');
    expect(Array.isArray(recommendations.recommendations)).toBeTruthy();
    
    console.log('✅ Optimization recommendations API working correctly');
  });

  test('Cost Report Export', async ({ request }) => {
    console.log('🔍 Testing cost report export...');
    
    // Test JSON export
    const jsonResponse = await request.get(`${API_BASE}/api/finops/cost-report`, {
      headers: {
        'Authorization': `Bearer ${authToken}`
      }
    });
    
    expect(jsonResponse.ok()).toBeTruthy();
    const jsonReport = await jsonResponse.json();
    expect(jsonReport).toHaveProperty('summary');
    expect(jsonReport).toHaveProperty('dailyBreakdown');
    
    // Test CSV export
    const csvResponse = await request.get(`${API_BASE}/api/finops/cost-report?format=csv`, {
      headers: {
        'Authorization': `Bearer ${authToken}`
      }
    });
    
    expect(csvResponse.ok()).toBeTruthy();
    const csvContent = await csvResponse.text();
    expect(csvContent).toContain('Date,Total Cost');
    
    console.log('✅ Cost report export working correctly');
  });

  test('Security - Rate Limiting', async ({ request }) => {
    console.log('🔍 Testing security rate limiting...');
    
    // Make multiple failed login attempts to trigger rate limiting
    const promises = [];
    for (let i = 0; i < 6; i++) {
      promises.push(
        request.post(`${API_BASE}/api/auth/login`, {
          data: {
            identifier: '<EMAIL>',
            password: 'wrongpassword'
          }
        })
      );
    }
    
    const responses = await Promise.all(promises);
    
    // At least one should be rate limited (429)
    const rateLimitedResponses = responses.filter(r => r.status() === 429);
    expect(rateLimitedResponses.length).toBeGreaterThan(0);
    
    console.log('✅ Rate limiting working correctly');
  });

  test('Performance - Response Times', async ({ request }) => {
    console.log('🔍 Testing API response times...');
    
    const startTime = Date.now();
    
    const response = await request.get(`${API_BASE}/api/finops/dashboard`, {
      headers: {
        'Authorization': `Bearer ${authToken}`
      }
    });
    
    const endTime = Date.now();
    const responseTime = endTime - startTime;
    
    expect(response.ok()).toBeTruthy();
    expect(responseTime).toBeLessThan(3000); // Should respond within 3 seconds
    
    console.log(`✅ Dashboard response time: ${responseTime}ms (target: <3000ms)`);
  });

  test.afterAll(async () => {
    console.log('🎉 Comprehensive FinOps testing completed successfully!');
  });
});

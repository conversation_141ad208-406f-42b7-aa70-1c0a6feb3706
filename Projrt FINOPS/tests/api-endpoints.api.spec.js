const { test, expect } = require('@playwright/test');

let authToken;

test.describe('API Endpoints', () => {
  test.beforeAll(async ({ request }) => {
    // Get authentication token
    const loginResponse = await request.post('/auth/login', {
      data: {
        identifier: '<EMAIL>',
        password: 'AdminPass123!'
      }
    });
    
    expect(loginResponse.ok()).toBeTruthy();
    const loginData = await loginResponse.json();
    authToken = loginData.data.token;
  });
  
  test.describe('Authentication API', () => {
    test('POST /auth/register - should register new user', async ({ request }) => {
      const timestamp = Date.now();
      const response = await request.post('/auth/register', {
        data: {
          username: `apitest${timestamp}`,
          email: `apitest${timestamp}@example.com`,
          password: 'ApiTest123!',
          firstName: 'API',
          lastName: 'Test'
        }
      });
      
      expect(response.ok()).toBeTruthy();
      const data = await response.json();
      expect(data.success).toBeTruthy();
      expect(data.data.user.email).toBe(`apitest${timestamp}@example.com`);
      expect(data.data.token).toBeDefined();
    });
    
    test('POST /auth/login - should login with valid credentials', async ({ request }) => {
      const response = await request.post('/auth/login', {
        data: {
          identifier: '<EMAIL>',
          password: 'AdminPass123!'
        }
      });
      
      expect(response.ok()).toBeTruthy();
      const data = await response.json();
      expect(data.success).toBeTruthy();
      expect(data.data.token).toBeDefined();
      expect(data.data.user.email).toBe('<EMAIL>');
    });
    
    test('POST /auth/login - should reject invalid credentials', async ({ request }) => {
      const response = await request.post('/auth/login', {
        data: {
          identifier: '<EMAIL>',
          password: 'wrongpassword'
        }
      });
      
      expect(response.status()).toBe(401);
      const data = await response.json();
      expect(data.success).toBeFalsy();
      expect(data.message).toContain('Invalid credentials');
    });
    
    test('GET /auth/profile - should get user profile with valid token', async ({ request }) => {
      const response = await request.get('/auth/profile', {
        headers: {
          'Authorization': `Bearer ${authToken}`
        }
      });
      
      expect(response.ok()).toBeTruthy();
      const data = await response.json();
      expect(data.success).toBeTruthy();
      expect(data.data.user.email).toBe('<EMAIL>');
    });
    
    test('GET /auth/profile - should reject invalid token', async ({ request }) => {
      const response = await request.get('/auth/profile', {
        headers: {
          'Authorization': 'Bearer invalid-token'
        }
      });
      
      expect(response.status()).toBe(401);
      const data = await response.json();
      expect(data.success).toBeFalsy();
    });
  });
  
  test.describe('FinOps API', () => {
    test('GET /finops/dashboard - should get dashboard data', async ({ request }) => {
      const response = await request.get('/finops/dashboard', {
        headers: {
          'Authorization': `Bearer ${authToken}`
        }
      });
      
      expect(response.ok()).toBeTruthy();
      const data = await response.json();
      expect(data.success).toBeTruthy();
      expect(data.data.summary).toBeDefined();
      expect(data.data.trends).toBeDefined();
      expect(data.data.budgets).toBeDefined();
    });
    
    test('GET /finops/cost-analysis - should get cost analysis', async ({ request }) => {
      const response = await request.get('/finops/cost-analysis', {
        headers: {
          'Authorization': `Bearer ${authToken}`
        }
      });
      
      expect(response.ok()).toBeTruthy();
      const data = await response.json();
      expect(data.success).toBeTruthy();
      expect(data.data.costs).toBeDefined();
    });
    
    test('GET /finops/optimization-recommendations - should get recommendations', async ({ request }) => {
      const response = await request.get('/finops/optimization-recommendations', {
        headers: {
          'Authorization': `Bearer ${authToken}`
        }
      });
      
      expect(response.ok()).toBeTruthy();
      const data = await response.json();
      expect(data.success).toBeTruthy();
      expect(data.data.recommendations).toBeDefined();
      expect(data.data.summary).toBeDefined();
    });
    
    test('GET /finops/cost-report - should generate cost report', async ({ request }) => {
      const response = await request.get('/finops/cost-report', {
        headers: {
          'Authorization': `Bearer ${authToken}`
        }
      });
      
      expect(response.ok()).toBeTruthy();
      const data = await response.json();
      expect(data.success).toBeTruthy();
      expect(data.data.metadata).toBeDefined();
      expect(data.data.summary).toBeDefined();
    });
    
    test('GET /finops/cost-report?format=csv - should generate CSV report', async ({ request }) => {
      const response = await request.get('/finops/cost-report?format=csv', {
        headers: {
          'Authorization': `Bearer ${authToken}`
        }
      });
      
      expect(response.ok()).toBeTruthy();
      expect(response.headers()['content-type']).toContain('text/csv');
    });
  });
  
  test.describe('Budget API', () => {
    let budgetId;
    
    test('POST /finops/budgets - should create budget', async ({ request }) => {
      const response = await request.post('/finops/budgets', {
        headers: {
          'Authorization': `Bearer ${authToken}`
        },
        data: {
          name: 'API Test Budget',
          description: 'Budget created via API test',
          scope: {
            type: 'team',
            filters: {
              teams: ['test-team']
            }
          },
          period: {
            type: 'monthly',
            startDate: new Date().toISOString(),
            endDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString()
          },
          amounts: {
            total: 2000,
            currency: 'EUR'
          }
        }
      });
      
      expect(response.ok()).toBeTruthy();
      const data = await response.json();
      expect(data.success).toBeTruthy();
      expect(data.data.name).toBe('API Test Budget');
      budgetId = data.data._id;
    });
    
    test('GET /finops/budgets - should list budgets', async ({ request }) => {
      const response = await request.get('/finops/budgets', {
        headers: {
          'Authorization': `Bearer ${authToken}`
        }
      });
      
      expect(response.ok()).toBeTruthy();
      const data = await response.json();
      expect(data.success).toBeTruthy();
      expect(Array.isArray(data.data)).toBeTruthy();
    });
    
    test('GET /finops/budgets/:id - should get budget by ID', async ({ request }) => {
      if (!budgetId) return;
      
      const response = await request.get(`/finops/budgets/${budgetId}`, {
        headers: {
          'Authorization': `Bearer ${authToken}`
        }
      });
      
      expect(response.ok()).toBeTruthy();
      const data = await response.json();
      expect(data.success).toBeTruthy();
      expect(data.data._id).toBe(budgetId);
    });
    
    test('PUT /finops/budgets/:id - should update budget', async ({ request }) => {
      if (!budgetId) return;
      
      const response = await request.put(`/finops/budgets/${budgetId}`, {
        headers: {
          'Authorization': `Bearer ${authToken}`
        },
        data: {
          name: 'Updated API Test Budget',
          amounts: {
            total: 2500,
            currency: 'EUR'
          }
        }
      });
      
      expect(response.ok()).toBeTruthy();
      const data = await response.json();
      expect(data.success).toBeTruthy();
      expect(data.data.name).toBe('Updated API Test Budget');
    });
    
    test('DELETE /finops/budgets/:id - should delete budget', async ({ request }) => {
      if (!budgetId) return;
      
      const response = await request.delete(`/finops/budgets/${budgetId}`, {
        headers: {
          'Authorization': `Bearer ${authToken}`
        }
      });
      
      expect(response.ok()).toBeTruthy();
      const data = await response.json();
      expect(data.success).toBeTruthy();
    });
  });
  
  test.describe('Todo API', () => {
    let todoId;
    
    test('POST /todos - should create todo', async ({ request }) => {
      const response = await request.post('/todos', {
        headers: {
          'Authorization': `Bearer ${authToken}`
        },
        data: {
          title: 'API Test Todo',
          description: 'Todo created via API test',
          priority: 'high'
        }
      });
      
      expect(response.ok()).toBeTruthy();
      const data = await response.json();
      expect(data.success).toBeTruthy();
      expect(data.data.title).toBe('API Test Todo');
      todoId = data.data._id;
    });
    
    test('GET /todos - should list todos', async ({ request }) => {
      const response = await request.get('/todos', {
        headers: {
          'Authorization': `Bearer ${authToken}`
        }
      });
      
      expect(response.ok()).toBeTruthy();
      const data = await response.json();
      expect(data.success).toBeTruthy();
      expect(Array.isArray(data.data)).toBeTruthy();
    });
    
    test('PUT /todos/:id - should update todo', async ({ request }) => {
      if (!todoId) return;
      
      const response = await request.put(`/todos/${todoId}`, {
        headers: {
          'Authorization': `Bearer ${authToken}`
        },
        data: {
          title: 'Updated API Test Todo',
          completed: true
        }
      });
      
      expect(response.ok()).toBeTruthy();
      const data = await response.json();
      expect(data.success).toBeTruthy();
      expect(data.data.completed).toBeTruthy();
    });
    
    test('DELETE /todos/:id - should delete todo', async ({ request }) => {
      if (!todoId) return;
      
      const response = await request.delete(`/todos/${todoId}`, {
        headers: {
          'Authorization': `Bearer ${authToken}`
        }
      });
      
      expect(response.ok()).toBeTruthy();
      const data = await response.json();
      expect(data.success).toBeTruthy();
    });
  });
  
  test.describe('Error Handling', () => {
    test('should return 404 for non-existent endpoints', async ({ request }) => {
      const response = await request.get('/non-existent-endpoint');
      expect(response.status()).toBe(404);
    });
    
    test('should return 401 for protected endpoints without auth', async ({ request }) => {
      const response = await request.get('/finops/dashboard');
      expect(response.status()).toBe(401);
    });
    
    test('should handle malformed JSON', async ({ request }) => {
      const response = await request.post('/auth/login', {
        data: 'invalid json'
      });
      expect(response.status()).toBe(400);
    });
  });
});

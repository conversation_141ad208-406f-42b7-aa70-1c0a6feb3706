const { test, expect } = require('@playwright/test');

test.describe('Authentication System', () => {
  test.describe('User Registration', () => {
    test('should register a new user successfully', async ({ page }) => {
      await page.goto('/');
      
      // Navigate to registration
      await page.click('button:has-text("S\'inscrire"), a:has-text("S\'inscrire")');
      
      // Fill registration form
      const timestamp = Date.now();
      await page.fill('input[name="firstName"]', 'Test');
      await page.fill('input[name="lastName"]', 'User');
      await page.fill('input[name="username"]', `testuser${timestamp}`);
      await page.fill('input[name="email"]', `test${timestamp}@example.com`);
      await page.fill('input[name="password"]', 'TestPass123!');
      await page.fill('input[name="confirmPassword"]', 'TestPass123!');
      
      // Submit registration
      await page.click('button[type="submit"]');
      
      // Verify successful registration
      await expect(page).toHaveURL(/dashboard|home/);
      await expect(page.locator('text=Test User')).toBeVisible();
    });
    
    test('should show validation errors for invalid registration', async ({ page }) => {
      await page.goto('/');
      
      // Navigate to registration
      await page.click('button:has-text("S\'inscrire"), a:has-text("S\'inscrire")');
      
      // Submit empty form
      await page.click('button[type="submit"]');
      
      // Check for validation errors
      await expect(page.locator('.invalid-feedback, .error')).toBeVisible();
    });
    
    test('should prevent registration with existing email', async ({ page }) => {
      await page.goto('/');
      
      // Navigate to registration
      await page.click('button:has-text("S\'inscrire"), a:has-text("S\'inscrire")');
      
      // Fill form with existing email
      await page.fill('input[name="firstName"]', 'Test');
      await page.fill('input[name="lastName"]', 'User');
      await page.fill('input[name="username"]', 'uniqueuser123');
      await page.fill('input[name="email"]', '<EMAIL>'); // Existing email
      await page.fill('input[name="password"]', 'TestPass123!');
      await page.fill('input[name="confirmPassword"]', 'TestPass123!');
      
      // Submit registration
      await page.click('button[type="submit"]');
      
      // Check for error message
      await expect(page.locator('text=Email already registered, text=already exists')).toBeVisible();
    });
  });
  
  test.describe('User Login', () => {
    test('should login with valid credentials', async ({ page }) => {
      await page.goto('/');
      
      // Fill login form
      await page.fill('input[name="identifier"]', '<EMAIL>');
      await page.fill('input[name="password"]', 'AdminPass123!');
      
      // Submit login
      await page.click('button[type="submit"]');
      
      // Verify successful login
      await expect(page).toHaveURL(/dashboard|home/);
      await expect(page.locator('text=FinOps Admin, text=Admin')).toBeVisible();
    });
    
    test('should show error for invalid credentials', async ({ page }) => {
      await page.goto('/');
      
      // Fill login form with invalid credentials
      await page.fill('input[name="identifier"]', '<EMAIL>');
      await page.fill('input[name="password"]', 'wrongpassword');
      
      // Submit login
      await page.click('button[type="submit"]');
      
      // Check for error message
      await expect(page.locator('text=Invalid credentials, .alert-danger')).toBeVisible();
    });
    
    test('should handle rate limiting after multiple failed attempts', async ({ page }) => {
      await page.goto('/');
      
      // Attempt multiple failed logins
      for (let i = 0; i < 6; i++) {
        await page.fill('input[name="identifier"]', '<EMAIL>');
        await page.fill('input[name="password"]', 'wrongpassword');
        await page.click('button[type="submit"]');
        
        if (i < 4) {
          await expect(page.locator('text=Invalid credentials')).toBeVisible();
        } else {
          // Should be rate limited after 5 attempts
          await expect(page.locator('text=Too many, text=rate limit')).toBeVisible();
          break;
        }
        
        // Wait a bit between attempts
        await page.waitForTimeout(1000);
      }
    });
  });
  
  test.describe('Authentication State', () => {
    test('should maintain authentication across page reloads', async ({ page }) => {
      await page.goto('/');
      
      // Login
      await page.fill('input[name="identifier"]', '<EMAIL>');
      await page.fill('input[name="password"]', 'AdminPass123!');
      await page.click('button[type="submit"]');
      
      // Verify login
      await expect(page).toHaveURL(/dashboard|home/);
      
      // Reload page
      await page.reload();
      
      // Should still be logged in
      await expect(page).toHaveURL(/dashboard|home/);
      await expect(page.locator('text=FinOps Admin, text=Admin')).toBeVisible();
    });
    
    test('should logout successfully', async ({ page }) => {
      await page.goto('/');
      
      // Login first
      await page.fill('input[name="identifier"]', '<EMAIL>');
      await page.fill('input[name="password"]', 'AdminPass123!');
      await page.click('button[type="submit"]');
      
      // Verify login
      await expect(page).toHaveURL(/dashboard|home/);
      
      // Logout
      await page.click('[data-testid="logout"], button:has-text("Logout"), button:has-text("Déconnexion")');
      
      // Should be redirected to login page
      await expect(page).toHaveURL(/login|\/$/);
      await expect(page.locator('input[name="identifier"], input[type="email"]')).toBeVisible();
    });
    
    test('should redirect to login when accessing protected routes without authentication', async ({ page }) => {
      // Clear any existing authentication
      await page.context().clearCookies();
      await page.context().clearPermissions();
      
      // Try to access protected route
      await page.goto('/dashboard');
      
      // Should be redirected to login
      await expect(page).toHaveURL(/login|\/$/);
      await expect(page.locator('input[name="identifier"], input[type="email"]')).toBeVisible();
    });
  });
  
  test.describe('Role-Based Access Control', () => {
    test('should allow FinOps manager to access FinOps features', async ({ page }) => {
      await page.goto('/');
      
      // Login as FinOps manager
      await page.fill('input[name="identifier"]', '<EMAIL>');
      await page.fill('input[name="password"]', 'AdminPass123!');
      await page.click('button[type="submit"]');
      
      // Navigate to FinOps dashboard
      await page.goto('/finops/dashboard');
      
      // Should have access
      await expect(page.locator('text=Dashboard, text=Cost, text=Budget')).toBeVisible();
    });
    
    test('should restrict regular user from admin features', async ({ page }) => {
      await page.goto('/');
      
      // Login as regular user
      await page.fill('input[name="identifier"]', '<EMAIL>');
      await page.fill('input[name="password"]', 'TestPass123!');
      await page.click('button[type="submit"]');
      
      // Try to access admin features
      await page.goto('/admin');
      
      // Should be denied or redirected
      await expect(page.locator('text=Access denied, text=Unauthorized, text=403')).toBeVisible();
    });
  });
});

const { test as setup, expect } = require('@playwright/test');
const path = require('path');

const authFile = path.join(__dirname, '../playwright/.auth/user.json');

setup('authenticate as <PERSON>Ops admin', async ({ page }) => {
  console.log('🔐 Setting up authentication for FinOps admin...');
  
  // Navigate to the application
  await page.goto('/');
  
  // Wait for the page to load
  await page.waitForLoadState('networkidle');
  
  // Check if we're already logged in
  const isLoggedIn = await page.locator('[data-testid="user-menu"]').isVisible().catch(() => false);
  
  if (!isLoggedIn) {
    // Look for login form or login button
    const loginButton = page.locator('button:has-text("Se connecter")').first();
    const loginForm = page.locator('form').first();
    
    if (await loginButton.isVisible()) {
      await loginButton.click();
      await page.waitForLoadState('networkidle');
    }
    
    // Fill in the login form
    await page.fill('input[name="identifier"], input[type="email"], input[placeholder*="email"], input[placeholder*="username"]', '<EMAIL>');
    await page.fill('input[name="password"], input[type="password"]', 'AdminPass123!');
    
    // Submit the form
    await page.click('button[type="submit"], button:has-text("Se connecter"), button:has-text("Login")');
    
    // Wait for successful login - look for dashboard or user menu
    await Promise.race([
      page.waitForURL('**/dashboard'),
      page.waitForSelector('[data-testid="user-menu"]'),
      page.waitForSelector('.dashboard'),
      page.waitForTimeout(10000)
    ]);
    
    // Verify we're logged in
    const currentUrl = page.url();
    console.log(`Current URL after login: ${currentUrl}`);
    
    // Check for success indicators
    const successIndicators = [
      page.locator('[data-testid="user-menu"]'),
      page.locator('.dashboard'),
      page.locator('text=Dashboard'),
      page.locator('text=FinOps'),
      page.locator('[data-testid="logout"]')
    ];
    
    let loginSuccessful = false;
    for (const indicator of successIndicators) {
      if (await indicator.isVisible().catch(() => false)) {
        loginSuccessful = true;
        break;
      }
    }
    
    if (!loginSuccessful) {
      // Check for error messages
      const errorMessage = await page.locator('.alert-danger, .error, [role="alert"]').textContent().catch(() => '');
      throw new Error(`Login failed. Error: ${errorMessage || 'Unknown error'}`);
    }
    
    console.log('✅ Successfully authenticated as FinOps admin');
  } else {
    console.log('ℹ️ Already authenticated');
  }
  
  // Save authentication state
  await page.context().storageState({ path: authFile });
  console.log('💾 Authentication state saved');
});

setup('verify API authentication', async ({ request }) => {
  console.log('🔍 Verifying API authentication...');
  
  // First, get a token by logging in via API
  const loginResponse = await request.post('/auth/login', {
    data: {
      identifier: '<EMAIL>',
      password: 'AdminPass123!'
    }
  });
  
  expect(loginResponse.ok()).toBeTruthy();
  const loginData = await loginResponse.json();
  expect(loginData.success).toBeTruthy();
  expect(loginData.data.token).toBeDefined();
  
  const token = loginData.data.token;
  console.log('✅ API authentication token obtained');
  
  // Test authenticated API endpoint
  const dashboardResponse = await request.get('/finops/dashboard', {
    headers: {
      'Authorization': `Bearer ${token}`
    }
  });
  
  expect(dashboardResponse.ok()).toBeTruthy();
  const dashboardData = await dashboardResponse.json();
  expect(dashboardData.success).toBeTruthy();
  
  console.log('✅ API authentication verified');
});

setup('seed test data', async ({ request }) => {
  console.log('🌱 Seeding test data...');
  
  try {
    // Login to get token
    const loginResponse = await request.post('/auth/login', {
      data: {
        identifier: '<EMAIL>',
        password: 'AdminPass123!'
      }
    });
    
    if (!loginResponse.ok()) {
      console.log('⚠️ Could not login for data seeding');
      return;
    }
    
    const loginData = await loginResponse.json();
    const token = loginData.data.token;
    
    // Create test budget
    const budgetResponse = await request.post('/finops/budgets', {
      headers: {
        'Authorization': `Bearer ${token}`
      },
      data: {
        name: 'Test Budget E2E',
        description: 'Budget for end-to-end testing',
        scope: {
          type: 'team',
          filters: {
            teams: ['test-team']
          }
        },
        period: {
          type: 'monthly',
          startDate: new Date().toISOString(),
          endDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString()
        },
        amounts: {
          total: 1000,
          currency: 'EUR'
        }
      }
    });
    
    if (budgetResponse.ok()) {
      console.log('✅ Test budget created');
    }
    
    // Create test todo
    const todoResponse = await request.post('/todos', {
      headers: {
        'Authorization': `Bearer ${token}`
      },
      data: {
        title: 'Test Todo E2E',
        description: 'Todo for end-to-end testing',
        priority: 'high'
      }
    });
    
    if (todoResponse.ok()) {
      console.log('✅ Test todo created');
    }
    
    console.log('✅ Test data seeding completed');
    
  } catch (error) {
    console.log('⚠️ Test data seeding failed:', error.message);
  }
});

const { chromium } = require('@playwright/test');
const fs = require('fs');
const path = require('path');

async function globalSetup(config) {
  console.log('🚀 Starting global setup for FinOps application tests...');
  
  // Create necessary directories
  const authDir = path.join(__dirname, '../playwright/.auth');
  if (!fs.existsSync(authDir)) {
    fs.mkdirSync(authDir, { recursive: true });
  }
  
  const testResultsDir = path.join(__dirname, '../test-results');
  if (!fs.existsSync(testResultsDir)) {
    fs.mkdirSync(testResultsDir, { recursive: true });
  }

  // Wait for backend to be ready
  console.log('⏳ Waiting for backend server to be ready...');
  await waitForServer('http://localhost:8080/health', 120000);
  
  // Wait for frontend to be ready
  console.log('⏳ Waiting for frontend server to be ready...');
  await waitForServer('http://localhost:3000', 120000);
  
  // Create test users and authenticate
  console.log('👤 Setting up test users and authentication...');
  await setupAuthentication();
  
  console.log('✅ Global setup completed successfully!');
}

async function waitForServer(url, timeout = 60000) {
  const startTime = Date.now();
  
  while (Date.now() - startTime < timeout) {
    try {
      const response = await fetch(url);
      if (response.ok) {
        console.log(`✅ Server at ${url} is ready`);
        return;
      }
    } catch (error) {
      // Server not ready yet, continue waiting
    }
    
    await new Promise(resolve => setTimeout(resolve, 2000));
  }
  
  throw new Error(`Server at ${url} did not become ready within ${timeout}ms`);
}

async function setupAuthentication() {
  const browser = await chromium.launch();
  const context = await browser.newContext();
  const page = await context.newPage();
  
  try {
    // Navigate to the application
    await page.goto('http://localhost:3000');
    
    // Check if we need to register a test user first
    const apiUrl = 'http://localhost:8080/api';
    
    // Try to register test users
    const testUsers = [
      {
        username: 'testuser',
        email: '<EMAIL>',
        password: 'TestPass123!',
        firstName: 'Test',
        lastName: 'User',
        role: 'user'
      },
      {
        username: 'finopsadmin',
        email: '<EMAIL>',
        password: 'AdminPass123!',
        firstName: 'FinOps',
        lastName: 'Admin',
        role: 'finops_manager'
      },
      {
        username: 'superadmin',
        email: '<EMAIL>',
        password: 'SuperPass123!',
        firstName: 'Super',
        lastName: 'Admin',
        role: 'admin'
      }
    ];
    
    for (const user of testUsers) {
      try {
        const response = await fetch(`${apiUrl}/auth/register`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(user),
        });
        
        if (response.ok) {
          console.log(`✅ Test user ${user.username} registered successfully`);
        } else {
          console.log(`ℹ️ Test user ${user.username} already exists or registration failed`);
        }
      } catch (error) {
        console.log(`⚠️ Failed to register test user ${user.username}:`, error.message);
      }
    }
    
    // Login as the main test user and save authentication state
    await page.goto('http://localhost:3000');
    
    // Wait for the page to load and check if we need to login
    await page.waitForLoadState('networkidle');
    
    // Check if login form is present (indicating we're not logged in)
    const loginForm = await page.locator('form').first();
    if (await loginForm.isVisible()) {
      // Fill login form
      await page.fill('input[name="identifier"]', '<EMAIL>');
      await page.fill('input[name="password"]', 'AdminPass123!');
      await page.click('button[type="submit"]');
      
      // Wait for successful login
      await page.waitForURL('**/dashboard', { timeout: 10000 });
      console.log('✅ Test user authenticated successfully');
    }
    
    // Save authentication state
    await page.context().storageState({ 
      path: path.join(__dirname, '../playwright/.auth/user.json') 
    });
    
    console.log('✅ Authentication state saved');
    
  } catch (error) {
    console.error('❌ Authentication setup failed:', error);
    throw error;
  } finally {
    await browser.close();
  }
}

module.exports = globalSetup;

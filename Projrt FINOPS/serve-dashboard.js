#!/usr/bin/env node

const express = require('express');
const path = require('path');
const cors = require('cors');

const app = express();
const PORT = 4000;

// Enable CORS for all origins
app.use(cors({
  origin: '*',
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With']
}));

// Add logging middleware
app.use((req, res, next) => {
  console.log(`${new Date().toISOString()} - ${req.method} ${req.url}`);
  next();
});

// Serve static files from current directory
app.use(express.static(__dirname));

// Serve the dashboard HTML file
app.get('/', (req, res) => {
  console.log('📊 Serving dashboard from root');
  res.sendFile(path.join(__dirname, 'finops-dashboard.html'));
});

app.get('/dashboard', (req, res) => {
  console.log('📊 Serving dashboard from /dashboard');
  res.sendFile(path.join(__dirname, 'finops-dashboard.html'));
});

// Health check
app.get('/health', (req, res) => {
  const healthData = {
    status: 'healthy',
    service: 'FinOps Dashboard Server',
    port: PORT,
    timestamp: new Date().toISOString()
  };
  console.log('💓 Health check requested:', healthData);
  res.json(healthData);
});

// Error handling
app.use((err, req, res, next) => {
  console.error('❌ Server error:', err);
  res.status(500).json({ error: 'Internal server error' });
});

const server = app.listen(PORT, '0.0.0.0', () => {
  console.log(`🎯 FinOps Dashboard Server running at http://localhost:${PORT}`);
  console.log(`📊 Dashboard available at: http://localhost:${PORT}/dashboard`);
  console.log(`🔗 Backend API: http://localhost:8080`);
  console.log(`✅ CORS enabled for all origins`);
  console.log(`🚀 Server started successfully on port ${PORT}`);
});

server.on('error', (err) => {
  if (err.code === 'EADDRINUSE') {
    console.error(`❌ Port ${PORT} is already in use. Please choose a different port.`);
  } else {
    console.error('❌ Server error:', err);
  }
  process.exit(1);
});

[{"/Users/<USER>/Desktop/Projrt FINOPS/frontend/src/index.js": "1", "/Users/<USER>/Desktop/Projrt FINOPS/frontend/src/App.js": "2", "/Users/<USER>/Desktop/Projrt FINOPS/frontend/src/components/TodoList.js": "3", "/Users/<USER>/Desktop/Projrt FINOPS/frontend/src/components/TodoStats.js": "4", "/Users/<USER>/Desktop/Projrt FINOPS/frontend/src/components/SearchTodo.js": "5", "/Users/<USER>/Desktop/Projrt FINOPS/frontend/src/services/TodoService.js": "6", "/Users/<USER>/Desktop/Projrt FINOPS/frontend/src/components/AddTodo.js": "7", "/Users/<USER>/Desktop/Projrt FINOPS/frontend/src/components/TodoItem.js": "8", "/Users/<USER>/Desktop/Projrt FINOPS/frontend/src/components/Auth/Login.js": "9", "/Users/<USER>/Desktop/Projrt FINOPS/frontend/src/components/Auth/Register.js": "10", "/Users/<USER>/Desktop/Projrt FINOPS/frontend/src/components/ErrorBoundary/ErrorBoundary.js": "11", "/Users/<USER>/Desktop/Projrt FINOPS/frontend/src/services/AuthService.js": "12"}, {"size": 279, "mtime": 1752389277252, "results": "13", "hashOfConfig": "14"}, {"size": 2809, "mtime": 1752499355768, "results": "15", "hashOfConfig": "14"}, {"size": 682, "mtime": 1752389319299, "results": "16", "hashOfConfig": "14"}, {"size": 3286, "mtime": 1752443630141, "results": "17", "hashOfConfig": "14"}, {"size": 1575, "mtime": 1752443587244, "results": "18", "hashOfConfig": "14"}, {"size": 911, "mtime": 1752443651058, "results": "19", "hashOfConfig": "14"}, {"size": 1288, "mtime": 1752389309463, "results": "20", "hashOfConfig": "14"}, {"size": 1154, "mtime": 1752389331820, "results": "21", "hashOfConfig": "14"}, {"size": 3692, "mtime": 1752491549133, "results": "22", "hashOfConfig": "14"}, {"size": 8829, "mtime": 1752491578484, "results": "23", "hashOfConfig": "14"}, {"size": 8094, "mtime": 1752492918584, "results": "24", "hashOfConfig": "14"}, {"size": 3943, "mtime": 1752491598838, "results": "25", "hashOfConfig": "14"}, {"filePath": "26", "messages": "27", "suppressedMessages": "28", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "b2d50m", {"filePath": "29", "messages": "30", "suppressedMessages": "31", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "32", "messages": "33", "suppressedMessages": "34", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "35", "messages": "36", "suppressedMessages": "37", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "38", "messages": "39", "suppressedMessages": "40", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "41", "messages": "42", "suppressedMessages": "43", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "44", "messages": "45", "suppressedMessages": "46", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "47", "messages": "48", "suppressedMessages": "49", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "50", "messages": "51", "suppressedMessages": "52", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "53", "messages": "54", "suppressedMessages": "55", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "56", "messages": "57", "suppressedMessages": "58", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "59", "messages": "60", "suppressedMessages": "61", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "/Users/<USER>/Desktop/Projrt FINOPS/frontend/src/index.js", [], [], "/Users/<USER>/Desktop/Projrt FINOPS/frontend/src/App.js", [], [], "/Users/<USER>/Desktop/Projrt FINOPS/frontend/src/components/TodoList.js", [], [], "/Users/<USER>/Desktop/Projrt FINOPS/frontend/src/components/TodoStats.js", [], [], "/Users/<USER>/Desktop/Projrt FINOPS/frontend/src/components/SearchTodo.js", [], [], "/Users/<USER>/Desktop/Projrt FINOPS/frontend/src/services/TodoService.js", ["62"], [], "/Users/<USER>/Desktop/Projrt FINOPS/frontend/src/components/AddTodo.js", [], [], "/Users/<USER>/Desktop/Projrt FINOPS/frontend/src/components/TodoItem.js", [], [], "/Users/<USER>/Desktop/Projrt FINOPS/frontend/src/components/Auth/Login.js", [], [], "/Users/<USER>/Desktop/Projrt FINOPS/frontend/src/components/Auth/Register.js", [], [], "/Users/<USER>/Desktop/Projrt FINOPS/frontend/src/components/ErrorBoundary/ErrorBoundary.js", [], [], "/Users/<USER>/Desktop/Projrt FINOPS/frontend/src/services/AuthService.js", ["63"], [], {"ruleId": "64", "severity": 1, "message": "65", "line": 54, "column": 1, "nodeType": "66", "endLine": 54, "endColumn": 34}, {"ruleId": "64", "severity": 1, "message": "65", "line": 163, "column": 1, "nodeType": "66", "endLine": 163, "endColumn": 34}, "import/no-anonymous-default-export", "Assign instance to a variable before exporting as module default", "ExportDefaultDeclaration"]
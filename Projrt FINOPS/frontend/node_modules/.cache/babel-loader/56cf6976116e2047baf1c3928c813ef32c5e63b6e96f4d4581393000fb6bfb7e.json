{"ast": null, "code": "import axios from 'axios';\nconst API_URL = process.env.REACT_APP_API_URL || 'http://localhost:8080/api';\nconst api = axios.create({\n  baseURL: API_URL,\n  headers: {\n    'Content-Type': 'application/json'\n  }\n});\n\n// Request interceptor to add auth token\napi.interceptors.request.use(config => {\n  const token = localStorage.getItem('token');\n  if (token) {\n    config.headers.Authorization = `Bearer ${token}`;\n  }\n  return config;\n}, error => {\n  return Promise.reject(error);\n});\n\n// Response interceptor to handle token refresh\napi.interceptors.response.use(response => response, async error => {\n  var _error$response;\n  const originalRequest = error.config;\n  if (((_error$response = error.response) === null || _error$response === void 0 ? void 0 : _error$response.status) === 401 && !originalRequest._retry) {\n    originalRequest._retry = true;\n    try {\n      const refreshToken = localStorage.getItem('refreshToken');\n      if (refreshToken) {\n        const response = await axios.post(`${API_URL}/auth/refresh-token`, {\n          refreshToken\n        });\n        if (response.data.success) {\n          const {\n            token,\n            refreshToken: newRefreshToken\n          } = response.data.data;\n          localStorage.setItem('token', token);\n          localStorage.setItem('refreshToken', newRefreshToken);\n\n          // Retry the original request with new token\n          originalRequest.headers.Authorization = `Bearer ${token}`;\n          return api(originalRequest);\n        }\n      }\n    } catch (refreshError) {\n      // Refresh failed, redirect to login\n      AuthService.logout();\n      window.location.href = '/login';\n    }\n  }\n  return Promise.reject(error);\n});\nclass AuthService {\n  async register(userData) {\n    try {\n      const response = await api.post('/auth/register', userData);\n      return response.data;\n    } catch (error) {\n      throw error;\n    }\n  }\n  async login(identifier, password) {\n    try {\n      const response = await api.post('/auth/login', {\n        identifier,\n        password\n      });\n      return response.data;\n    } catch (error) {\n      throw error;\n    }\n  }\n  async logout() {\n    try {\n      await api.post('/auth/logout');\n    } catch (error) {\n      console.error('Logout error:', error);\n    } finally {\n      // Clear local storage regardless of API call success\n      localStorage.removeItem('token');\n      localStorage.removeItem('refreshToken');\n      localStorage.removeItem('user');\n    }\n  }\n  async getProfile() {\n    try {\n      const response = await api.get('/auth/profile');\n      return response.data;\n    } catch (error) {\n      throw error;\n    }\n  }\n  async refreshToken() {\n    try {\n      const refreshToken = localStorage.getItem('refreshToken');\n      if (!refreshToken) {\n        throw new Error('No refresh token available');\n      }\n      const response = await api.post('/auth/refresh-token', {\n        refreshToken\n      });\n      if (response.data.success) {\n        const {\n          token,\n          refreshToken: newRefreshToken\n        } = response.data.data;\n        localStorage.setItem('token', token);\n        localStorage.setItem('refreshToken', newRefreshToken);\n        return response.data;\n      }\n      throw new Error('Token refresh failed');\n    } catch (error) {\n      this.logout();\n      throw error;\n    }\n  }\n  getCurrentUser() {\n    try {\n      const user = localStorage.getItem('user');\n      return user ? JSON.parse(user) : null;\n    } catch (error) {\n      console.error('Error parsing user data:', error);\n      return null;\n    }\n  }\n  getToken() {\n    return localStorage.getItem('token');\n  }\n  isAuthenticated() {\n    const token = this.getToken();\n    const user = this.getCurrentUser();\n    return !!(token && user);\n  }\n  hasRole(role) {\n    const user = this.getCurrentUser();\n    return user && user.role === role;\n  }\n  hasAnyRole(roles) {\n    const user = this.getCurrentUser();\n    return user && roles.includes(user.role);\n  }\n}\nexport default new AuthService();", "map": {"version": 3, "names": ["axios", "API_URL", "process", "env", "REACT_APP_API_URL", "api", "create", "baseURL", "headers", "interceptors", "request", "use", "config", "token", "localStorage", "getItem", "Authorization", "error", "Promise", "reject", "response", "_error$response", "originalRequest", "status", "_retry", "refreshToken", "post", "data", "success", "newRefreshToken", "setItem", "refreshError", "AuthService", "logout", "window", "location", "href", "register", "userData", "login", "identifier", "password", "console", "removeItem", "getProfile", "get", "Error", "getCurrentUser", "user", "JSON", "parse", "getToken", "isAuthenticated", "hasRole", "role", "hasAnyRole", "roles", "includes"], "sources": ["/Users/<USER>/Desktop/Projrt FINOPS/frontend/src/services/AuthService.js"], "sourcesContent": ["import axios from 'axios';\n\nconst API_URL = process.env.REACT_APP_API_URL || 'http://localhost:8080/api';\n\nconst api = axios.create({\n  baseURL: API_URL,\n  headers: {\n    'Content-Type': 'application/json',\n  },\n});\n\n// Request interceptor to add auth token\napi.interceptors.request.use(\n  (config) => {\n    const token = localStorage.getItem('token');\n    if (token) {\n      config.headers.Authorization = `Bearer ${token}`;\n    }\n    return config;\n  },\n  (error) => {\n    return Promise.reject(error);\n  }\n);\n\n// Response interceptor to handle token refresh\napi.interceptors.response.use(\n  (response) => response,\n  async (error) => {\n    const originalRequest = error.config;\n\n    if (error.response?.status === 401 && !originalRequest._retry) {\n      originalRequest._retry = true;\n\n      try {\n        const refreshToken = localStorage.getItem('refreshToken');\n        if (refreshToken) {\n          const response = await axios.post(`${API_URL}/auth/refresh-token`, {\n            refreshToken\n          });\n\n          if (response.data.success) {\n            const { token, refreshToken: newRefreshToken } = response.data.data;\n            localStorage.setItem('token', token);\n            localStorage.setItem('refreshToken', newRefreshToken);\n\n            // Retry the original request with new token\n            originalRequest.headers.Authorization = `Bearer ${token}`;\n            return api(originalRequest);\n          }\n        }\n      } catch (refreshError) {\n        // Refresh failed, redirect to login\n        AuthService.logout();\n        window.location.href = '/login';\n      }\n    }\n\n    return Promise.reject(error);\n  }\n);\n\nclass AuthService {\n  async register(userData) {\n    try {\n      const response = await api.post('/auth/register', userData);\n      return response.data;\n    } catch (error) {\n      throw error;\n    }\n  }\n\n  async login(identifier, password) {\n    try {\n      const response = await api.post('/auth/login', {\n        identifier,\n        password\n      });\n      return response.data;\n    } catch (error) {\n      throw error;\n    }\n  }\n\n  async logout() {\n    try {\n      await api.post('/auth/logout');\n    } catch (error) {\n      console.error('Logout error:', error);\n    } finally {\n      // Clear local storage regardless of API call success\n      localStorage.removeItem('token');\n      localStorage.removeItem('refreshToken');\n      localStorage.removeItem('user');\n    }\n  }\n\n  async getProfile() {\n    try {\n      const response = await api.get('/auth/profile');\n      return response.data;\n    } catch (error) {\n      throw error;\n    }\n  }\n\n  async refreshToken() {\n    try {\n      const refreshToken = localStorage.getItem('refreshToken');\n      if (!refreshToken) {\n        throw new Error('No refresh token available');\n      }\n\n      const response = await api.post('/auth/refresh-token', {\n        refreshToken\n      });\n\n      if (response.data.success) {\n        const { token, refreshToken: newRefreshToken } = response.data.data;\n        localStorage.setItem('token', token);\n        localStorage.setItem('refreshToken', newRefreshToken);\n        return response.data;\n      }\n\n      throw new Error('Token refresh failed');\n    } catch (error) {\n      this.logout();\n      throw error;\n    }\n  }\n\n  getCurrentUser() {\n    try {\n      const user = localStorage.getItem('user');\n      return user ? JSON.parse(user) : null;\n    } catch (error) {\n      console.error('Error parsing user data:', error);\n      return null;\n    }\n  }\n\n  getToken() {\n    return localStorage.getItem('token');\n  }\n\n  isAuthenticated() {\n    const token = this.getToken();\n    const user = this.getCurrentUser();\n    return !!(token && user);\n  }\n\n  hasRole(role) {\n    const user = this.getCurrentUser();\n    return user && user.role === role;\n  }\n\n  hasAnyRole(roles) {\n    const user = this.getCurrentUser();\n    return user && roles.includes(user.role);\n  }\n}\n\nexport default new AuthService();\n"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;AAEzB,MAAMC,OAAO,GAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB,IAAI,2BAA2B;AAE5E,MAAMC,GAAG,GAAGL,KAAK,CAACM,MAAM,CAAC;EACvBC,OAAO,EAAEN,OAAO;EAChBO,OAAO,EAAE;IACP,cAAc,EAAE;EAClB;AACF,CAAC,CAAC;;AAEF;AACAH,GAAG,CAACI,YAAY,CAACC,OAAO,CAACC,GAAG,CACzBC,MAAM,IAAK;EACV,MAAMC,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;EAC3C,IAAIF,KAAK,EAAE;IACTD,MAAM,CAACJ,OAAO,CAACQ,aAAa,GAAG,UAAUH,KAAK,EAAE;EAClD;EACA,OAAOD,MAAM;AACf,CAAC,EACAK,KAAK,IAAK;EACT,OAAOC,OAAO,CAACC,MAAM,CAACF,KAAK,CAAC;AAC9B,CACF,CAAC;;AAED;AACAZ,GAAG,CAACI,YAAY,CAACW,QAAQ,CAACT,GAAG,CAC1BS,QAAQ,IAAKA,QAAQ,EACtB,MAAOH,KAAK,IAAK;EAAA,IAAAI,eAAA;EACf,MAAMC,eAAe,GAAGL,KAAK,CAACL,MAAM;EAEpC,IAAI,EAAAS,eAAA,GAAAJ,KAAK,CAACG,QAAQ,cAAAC,eAAA,uBAAdA,eAAA,CAAgBE,MAAM,MAAK,GAAG,IAAI,CAACD,eAAe,CAACE,MAAM,EAAE;IAC7DF,eAAe,CAACE,MAAM,GAAG,IAAI;IAE7B,IAAI;MACF,MAAMC,YAAY,GAAGX,YAAY,CAACC,OAAO,CAAC,cAAc,CAAC;MACzD,IAAIU,YAAY,EAAE;QAChB,MAAML,QAAQ,GAAG,MAAMpB,KAAK,CAAC0B,IAAI,CAAC,GAAGzB,OAAO,qBAAqB,EAAE;UACjEwB;QACF,CAAC,CAAC;QAEF,IAAIL,QAAQ,CAACO,IAAI,CAACC,OAAO,EAAE;UACzB,MAAM;YAAEf,KAAK;YAAEY,YAAY,EAAEI;UAAgB,CAAC,GAAGT,QAAQ,CAACO,IAAI,CAACA,IAAI;UACnEb,YAAY,CAACgB,OAAO,CAAC,OAAO,EAAEjB,KAAK,CAAC;UACpCC,YAAY,CAACgB,OAAO,CAAC,cAAc,EAAED,eAAe,CAAC;;UAErD;UACAP,eAAe,CAACd,OAAO,CAACQ,aAAa,GAAG,UAAUH,KAAK,EAAE;UACzD,OAAOR,GAAG,CAACiB,eAAe,CAAC;QAC7B;MACF;IACF,CAAC,CAAC,OAAOS,YAAY,EAAE;MACrB;MACAC,WAAW,CAACC,MAAM,CAAC,CAAC;MACpBC,MAAM,CAACC,QAAQ,CAACC,IAAI,GAAG,QAAQ;IACjC;EACF;EAEA,OAAOlB,OAAO,CAACC,MAAM,CAACF,KAAK,CAAC;AAC9B,CACF,CAAC;AAED,MAAMe,WAAW,CAAC;EAChB,MAAMK,QAAQA,CAACC,QAAQ,EAAE;IACvB,IAAI;MACF,MAAMlB,QAAQ,GAAG,MAAMf,GAAG,CAACqB,IAAI,CAAC,gBAAgB,EAAEY,QAAQ,CAAC;MAC3D,OAAOlB,QAAQ,CAACO,IAAI;IACtB,CAAC,CAAC,OAAOV,KAAK,EAAE;MACd,MAAMA,KAAK;IACb;EACF;EAEA,MAAMsB,KAAKA,CAACC,UAAU,EAAEC,QAAQ,EAAE;IAChC,IAAI;MACF,MAAMrB,QAAQ,GAAG,MAAMf,GAAG,CAACqB,IAAI,CAAC,aAAa,EAAE;QAC7Cc,UAAU;QACVC;MACF,CAAC,CAAC;MACF,OAAOrB,QAAQ,CAACO,IAAI;IACtB,CAAC,CAAC,OAAOV,KAAK,EAAE;MACd,MAAMA,KAAK;IACb;EACF;EAEA,MAAMgB,MAAMA,CAAA,EAAG;IACb,IAAI;MACF,MAAM5B,GAAG,CAACqB,IAAI,CAAC,cAAc,CAAC;IAChC,CAAC,CAAC,OAAOT,KAAK,EAAE;MACdyB,OAAO,CAACzB,KAAK,CAAC,eAAe,EAAEA,KAAK,CAAC;IACvC,CAAC,SAAS;MACR;MACAH,YAAY,CAAC6B,UAAU,CAAC,OAAO,CAAC;MAChC7B,YAAY,CAAC6B,UAAU,CAAC,cAAc,CAAC;MACvC7B,YAAY,CAAC6B,UAAU,CAAC,MAAM,CAAC;IACjC;EACF;EAEA,MAAMC,UAAUA,CAAA,EAAG;IACjB,IAAI;MACF,MAAMxB,QAAQ,GAAG,MAAMf,GAAG,CAACwC,GAAG,CAAC,eAAe,CAAC;MAC/C,OAAOzB,QAAQ,CAACO,IAAI;IACtB,CAAC,CAAC,OAAOV,KAAK,EAAE;MACd,MAAMA,KAAK;IACb;EACF;EAEA,MAAMQ,YAAYA,CAAA,EAAG;IACnB,IAAI;MACF,MAAMA,YAAY,GAAGX,YAAY,CAACC,OAAO,CAAC,cAAc,CAAC;MACzD,IAAI,CAACU,YAAY,EAAE;QACjB,MAAM,IAAIqB,KAAK,CAAC,4BAA4B,CAAC;MAC/C;MAEA,MAAM1B,QAAQ,GAAG,MAAMf,GAAG,CAACqB,IAAI,CAAC,qBAAqB,EAAE;QACrDD;MACF,CAAC,CAAC;MAEF,IAAIL,QAAQ,CAACO,IAAI,CAACC,OAAO,EAAE;QACzB,MAAM;UAAEf,KAAK;UAAEY,YAAY,EAAEI;QAAgB,CAAC,GAAGT,QAAQ,CAACO,IAAI,CAACA,IAAI;QACnEb,YAAY,CAACgB,OAAO,CAAC,OAAO,EAAEjB,KAAK,CAAC;QACpCC,YAAY,CAACgB,OAAO,CAAC,cAAc,EAAED,eAAe,CAAC;QACrD,OAAOT,QAAQ,CAACO,IAAI;MACtB;MAEA,MAAM,IAAImB,KAAK,CAAC,sBAAsB,CAAC;IACzC,CAAC,CAAC,OAAO7B,KAAK,EAAE;MACd,IAAI,CAACgB,MAAM,CAAC,CAAC;MACb,MAAMhB,KAAK;IACb;EACF;EAEA8B,cAAcA,CAAA,EAAG;IACf,IAAI;MACF,MAAMC,IAAI,GAAGlC,YAAY,CAACC,OAAO,CAAC,MAAM,CAAC;MACzC,OAAOiC,IAAI,GAAGC,IAAI,CAACC,KAAK,CAACF,IAAI,CAAC,GAAG,IAAI;IACvC,CAAC,CAAC,OAAO/B,KAAK,EAAE;MACdyB,OAAO,CAACzB,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;MAChD,OAAO,IAAI;IACb;EACF;EAEAkC,QAAQA,CAAA,EAAG;IACT,OAAOrC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;EACtC;EAEAqC,eAAeA,CAAA,EAAG;IAChB,MAAMvC,KAAK,GAAG,IAAI,CAACsC,QAAQ,CAAC,CAAC;IAC7B,MAAMH,IAAI,GAAG,IAAI,CAACD,cAAc,CAAC,CAAC;IAClC,OAAO,CAAC,EAAElC,KAAK,IAAImC,IAAI,CAAC;EAC1B;EAEAK,OAAOA,CAACC,IAAI,EAAE;IACZ,MAAMN,IAAI,GAAG,IAAI,CAACD,cAAc,CAAC,CAAC;IAClC,OAAOC,IAAI,IAAIA,IAAI,CAACM,IAAI,KAAKA,IAAI;EACnC;EAEAC,UAAUA,CAACC,KAAK,EAAE;IAChB,MAAMR,IAAI,GAAG,IAAI,CAACD,cAAc,CAAC,CAAC;IAClC,OAAOC,IAAI,IAAIQ,KAAK,CAACC,QAAQ,CAACT,IAAI,CAACM,IAAI,CAAC;EAC1C;AACF;AAEA,eAAe,IAAItB,WAAW,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
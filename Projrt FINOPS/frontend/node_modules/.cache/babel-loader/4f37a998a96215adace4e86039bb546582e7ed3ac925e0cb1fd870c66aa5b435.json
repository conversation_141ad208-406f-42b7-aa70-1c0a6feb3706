{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Desktop/Projrt FINOPS/frontend/src/components/Auth/Login.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport AuthService from '../../services/AuthService';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst Login = ({\n  onLogin,\n  onSwitchToRegister\n}) => {\n  _s();\n  const [formData, setFormData] = useState({\n    identifier: '',\n    password: ''\n  });\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState('');\n  const handleChange = e => {\n    setFormData({\n      ...formData,\n      [e.target.name]: e.target.value\n    });\n    setError(''); // Clear error when user types\n  };\n  const handleSubmit = async e => {\n    e.preventDefault();\n    setLoading(true);\n    setError('');\n    try {\n      const response = await AuthService.login(formData.identifier, formData.password);\n      if (response.success) {\n        // Store tokens\n        localStorage.setItem('token', response.data.token);\n        localStorage.setItem('refreshToken', response.data.refreshToken);\n        localStorage.setItem('user', JSON.stringify(response.data.user));\n\n        // Call parent callback\n        onLogin(response.data.user);\n      } else {\n        setError(response.message || 'Login failed');\n      }\n    } catch (error) {\n      var _error$response, _error$response$data;\n      console.error('Login error:', error);\n      setError(((_error$response = error.response) === null || _error$response === void 0 ? void 0 : (_error$response$data = _error$response.data) === null || _error$response$data === void 0 ? void 0 : _error$response$data.message) || 'Login failed. Please try again.');\n    } finally {\n      setLoading(false);\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"card\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"card-header\",\n      children: /*#__PURE__*/_jsxDEV(\"h4\", {\n        className: \"mb-0\",\n        children: \"Se connecter\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 50,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 49,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"card-body\",\n      children: [error && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"alert alert-danger\",\n        role: \"alert\",\n        children: error\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 54,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n        onSubmit: handleSubmit,\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mb-3\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            htmlFor: \"identifier\",\n            className: \"form-label\",\n            children: \"Email ou nom d'utilisateur\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 61,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"text\",\n            className: \"form-control\",\n            id: \"identifier\",\n            name: \"identifier\",\n            value: formData.identifier,\n            onChange: handleChange,\n            required: true,\n            disabled: loading,\n            placeholder: \"Entrez votre email ou nom d'utilisateur\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 64,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 60,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mb-3\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            htmlFor: \"password\",\n            className: \"form-label\",\n            children: \"Mot de passe\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 78,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"password\",\n            className: \"form-control\",\n            id: \"password\",\n            name: \"password\",\n            value: formData.password,\n            onChange: handleChange,\n            required: true,\n            disabled: loading,\n            placeholder: \"Entrez votre mot de passe\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 81,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 77,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"d-grid gap-2\",\n          children: /*#__PURE__*/_jsxDEV(\"button\", {\n            type: \"submit\",\n            className: \"btn btn-primary\",\n            disabled: loading,\n            children: loading ? /*#__PURE__*/_jsxDEV(_Fragment, {\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"spinner-border spinner-border-sm me-2\",\n                role: \"status\",\n                \"aria-hidden\": \"true\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 102,\n                columnNumber: 19\n              }, this), \"Connexion...\"]\n            }, void 0, true) : 'Se connecter'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 95,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 94,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 59,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center mt-3\",\n        children: /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"mb-0\",\n          children: [\"Pas encore de compte ?\", ' ', /*#__PURE__*/_jsxDEV(\"button\", {\n            type: \"button\",\n            className: \"btn btn-link p-0\",\n            onClick: onSwitchToRegister,\n            disabled: loading,\n            children: \"S'inscrire\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 115,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 113,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 112,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 52,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 48,\n    columnNumber: 5\n  }, this);\n};\n_s(Login, \"IJmR8rwrqEbbzqq+egyvGOSoUnA=\");\n_c = Login;\nexport default Login;\nvar _c;\n$RefreshReg$(_c, \"Login\");", "map": {"version": 3, "names": ["React", "useState", "AuthService", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "<PERSON><PERSON>", "onLogin", "onSwitchToRegister", "_s", "formData", "setFormData", "identifier", "password", "loading", "setLoading", "error", "setError", "handleChange", "e", "target", "name", "value", "handleSubmit", "preventDefault", "response", "login", "success", "localStorage", "setItem", "data", "token", "refreshToken", "JSON", "stringify", "user", "message", "_error$response", "_error$response$data", "console", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "role", "onSubmit", "htmlFor", "type", "id", "onChange", "required", "disabled", "placeholder", "onClick", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Desktop/Projrt FINOPS/frontend/src/components/Auth/Login.js"], "sourcesContent": ["import React, { useState } from 'react';\nimport AuthService from '../../services/AuthService';\n\nconst Login = ({ onLogin, onSwitchToRegister }) => {\n  const [formData, setFormData] = useState({\n    identifier: '',\n    password: ''\n  });\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState('');\n\n  const handleChange = (e) => {\n    setFormData({\n      ...formData,\n      [e.target.name]: e.target.value\n    });\n    setError(''); // Clear error when user types\n  };\n\n  const handleSubmit = async (e) => {\n    e.preventDefault();\n    setLoading(true);\n    setError('');\n\n    try {\n      const response = await AuthService.login(formData.identifier, formData.password);\n      \n      if (response.success) {\n        // Store tokens\n        localStorage.setItem('token', response.data.token);\n        localStorage.setItem('refreshToken', response.data.refreshToken);\n        localStorage.setItem('user', JSON.stringify(response.data.user));\n        \n        // Call parent callback\n        onLogin(response.data.user);\n      } else {\n        setError(response.message || 'Login failed');\n      }\n    } catch (error) {\n      console.error('Login error:', error);\n      setError(error.response?.data?.message || 'Login failed. Please try again.');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  return (\n    <div className=\"card\">\n      <div className=\"card-header\">\n        <h4 className=\"mb-0\">Se connecter</h4>\n      </div>\n      <div className=\"card-body\">\n        {error && (\n          <div className=\"alert alert-danger\" role=\"alert\">\n            {error}\n          </div>\n        )}\n        \n        <form onSubmit={handleSubmit}>\n          <div className=\"mb-3\">\n            <label htmlFor=\"identifier\" className=\"form-label\">\n              Email ou nom d'utilisateur\n            </label>\n            <input\n              type=\"text\"\n              className=\"form-control\"\n              id=\"identifier\"\n              name=\"identifier\"\n              value={formData.identifier}\n              onChange={handleChange}\n              required\n              disabled={loading}\n              placeholder=\"Entrez votre email ou nom d'utilisateur\"\n            />\n          </div>\n          \n          <div className=\"mb-3\">\n            <label htmlFor=\"password\" className=\"form-label\">\n              Mot de passe\n            </label>\n            <input\n              type=\"password\"\n              className=\"form-control\"\n              id=\"password\"\n              name=\"password\"\n              value={formData.password}\n              onChange={handleChange}\n              required\n              disabled={loading}\n              placeholder=\"Entrez votre mot de passe\"\n            />\n          </div>\n          \n          <div className=\"d-grid gap-2\">\n            <button\n              type=\"submit\"\n              className=\"btn btn-primary\"\n              disabled={loading}\n            >\n              {loading ? (\n                <>\n                  <span className=\"spinner-border spinner-border-sm me-2\" role=\"status\" aria-hidden=\"true\"></span>\n                  Connexion...\n                </>\n              ) : (\n                'Se connecter'\n              )}\n            </button>\n          </div>\n        </form>\n        \n        <div className=\"text-center mt-3\">\n          <p className=\"mb-0\">\n            Pas encore de compte ?{' '}\n            <button\n              type=\"button\"\n              className=\"btn btn-link p-0\"\n              onClick={onSwitchToRegister}\n              disabled={loading}\n            >\n              S'inscrire\n            </button>\n          </p>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default Login;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,OAAOC,WAAW,MAAM,4BAA4B;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAErD,MAAMC,KAAK,GAAGA,CAAC;EAAEC,OAAO;EAAEC;AAAmB,CAAC,KAAK;EAAAC,EAAA;EACjD,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGX,QAAQ,CAAC;IACvCY,UAAU,EAAE,EAAE;IACdC,QAAQ,EAAE;EACZ,CAAC,CAAC;EACF,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGf,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACgB,KAAK,EAAEC,QAAQ,CAAC,GAAGjB,QAAQ,CAAC,EAAE,CAAC;EAEtC,MAAMkB,YAAY,GAAIC,CAAC,IAAK;IAC1BR,WAAW,CAAC;MACV,GAAGD,QAAQ;MACX,CAACS,CAAC,CAACC,MAAM,CAACC,IAAI,GAAGF,CAAC,CAACC,MAAM,CAACE;IAC5B,CAAC,CAAC;IACFL,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAC;EAChB,CAAC;EAED,MAAMM,YAAY,GAAG,MAAOJ,CAAC,IAAK;IAChCA,CAAC,CAACK,cAAc,CAAC,CAAC;IAClBT,UAAU,CAAC,IAAI,CAAC;IAChBE,QAAQ,CAAC,EAAE,CAAC;IAEZ,IAAI;MACF,MAAMQ,QAAQ,GAAG,MAAMxB,WAAW,CAACyB,KAAK,CAAChB,QAAQ,CAACE,UAAU,EAAEF,QAAQ,CAACG,QAAQ,CAAC;MAEhF,IAAIY,QAAQ,CAACE,OAAO,EAAE;QACpB;QACAC,YAAY,CAACC,OAAO,CAAC,OAAO,EAAEJ,QAAQ,CAACK,IAAI,CAACC,KAAK,CAAC;QAClDH,YAAY,CAACC,OAAO,CAAC,cAAc,EAAEJ,QAAQ,CAACK,IAAI,CAACE,YAAY,CAAC;QAChEJ,YAAY,CAACC,OAAO,CAAC,MAAM,EAAEI,IAAI,CAACC,SAAS,CAACT,QAAQ,CAACK,IAAI,CAACK,IAAI,CAAC,CAAC;;QAEhE;QACA5B,OAAO,CAACkB,QAAQ,CAACK,IAAI,CAACK,IAAI,CAAC;MAC7B,CAAC,MAAM;QACLlB,QAAQ,CAACQ,QAAQ,CAACW,OAAO,IAAI,cAAc,CAAC;MAC9C;IACF,CAAC,CAAC,OAAOpB,KAAK,EAAE;MAAA,IAAAqB,eAAA,EAAAC,oBAAA;MACdC,OAAO,CAACvB,KAAK,CAAC,cAAc,EAAEA,KAAK,CAAC;MACpCC,QAAQ,CAAC,EAAAoB,eAAA,GAAArB,KAAK,CAACS,QAAQ,cAAAY,eAAA,wBAAAC,oBAAA,GAAdD,eAAA,CAAgBP,IAAI,cAAAQ,oBAAA,uBAApBA,oBAAA,CAAsBF,OAAO,KAAI,iCAAiC,CAAC;IAC9E,CAAC,SAAS;MACRrB,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,oBACEZ,OAAA;IAAKqC,SAAS,EAAC,MAAM;IAAAC,QAAA,gBACnBtC,OAAA;MAAKqC,SAAS,EAAC,aAAa;MAAAC,QAAA,eAC1BtC,OAAA;QAAIqC,SAAS,EAAC,MAAM;QAAAC,QAAA,EAAC;MAAY;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACnC,CAAC,eACN1C,OAAA;MAAKqC,SAAS,EAAC,WAAW;MAAAC,QAAA,GACvBzB,KAAK,iBACJb,OAAA;QAAKqC,SAAS,EAAC,oBAAoB;QAACM,IAAI,EAAC,OAAO;QAAAL,QAAA,EAC7CzB;MAAK;QAAA0B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN,eAED1C,OAAA;QAAM4C,QAAQ,EAAExB,YAAa;QAAAkB,QAAA,gBAC3BtC,OAAA;UAAKqC,SAAS,EAAC,MAAM;UAAAC,QAAA,gBACnBtC,OAAA;YAAO6C,OAAO,EAAC,YAAY;YAACR,SAAS,EAAC,YAAY;YAAAC,QAAA,EAAC;UAEnD;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACR1C,OAAA;YACE8C,IAAI,EAAC,MAAM;YACXT,SAAS,EAAC,cAAc;YACxBU,EAAE,EAAC,YAAY;YACf7B,IAAI,EAAC,YAAY;YACjBC,KAAK,EAAEZ,QAAQ,CAACE,UAAW;YAC3BuC,QAAQ,EAAEjC,YAAa;YACvBkC,QAAQ;YACRC,QAAQ,EAAEvC,OAAQ;YAClBwC,WAAW,EAAC;UAAyC;YAAAZ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAEN1C,OAAA;UAAKqC,SAAS,EAAC,MAAM;UAAAC,QAAA,gBACnBtC,OAAA;YAAO6C,OAAO,EAAC,UAAU;YAACR,SAAS,EAAC,YAAY;YAAAC,QAAA,EAAC;UAEjD;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACR1C,OAAA;YACE8C,IAAI,EAAC,UAAU;YACfT,SAAS,EAAC,cAAc;YACxBU,EAAE,EAAC,UAAU;YACb7B,IAAI,EAAC,UAAU;YACfC,KAAK,EAAEZ,QAAQ,CAACG,QAAS;YACzBsC,QAAQ,EAAEjC,YAAa;YACvBkC,QAAQ;YACRC,QAAQ,EAAEvC,OAAQ;YAClBwC,WAAW,EAAC;UAA2B;YAAAZ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAEN1C,OAAA;UAAKqC,SAAS,EAAC,cAAc;UAAAC,QAAA,eAC3BtC,OAAA;YACE8C,IAAI,EAAC,QAAQ;YACbT,SAAS,EAAC,iBAAiB;YAC3Ba,QAAQ,EAAEvC,OAAQ;YAAA2B,QAAA,EAEjB3B,OAAO,gBACNX,OAAA,CAAAE,SAAA;cAAAoC,QAAA,gBACEtC,OAAA;gBAAMqC,SAAS,EAAC,uCAAuC;gBAACM,IAAI,EAAC,QAAQ;gBAAC,eAAY;cAAM;gBAAAJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,gBAElG;YAAA,eAAE,CAAC,GAEH;UACD;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACK;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,eAEP1C,OAAA;QAAKqC,SAAS,EAAC,kBAAkB;QAAAC,QAAA,eAC/BtC,OAAA;UAAGqC,SAAS,EAAC,MAAM;UAAAC,QAAA,GAAC,wBACI,EAAC,GAAG,eAC1BtC,OAAA;YACE8C,IAAI,EAAC,QAAQ;YACbT,SAAS,EAAC,kBAAkB;YAC5Be,OAAO,EAAE/C,kBAAmB;YAC5B6C,QAAQ,EAAEvC,OAAQ;YAAA2B,QAAA,EACnB;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACpC,EAAA,CA5HIH,KAAK;AAAAkD,EAAA,GAALlD,KAAK;AA8HX,eAAeA,KAAK;AAAC,IAAAkD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Desktop/Projrt FINOPS/frontend/src/components/Auth/Register.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport AuthService from '../../services/AuthService';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst Register = ({\n  onRegister,\n  onSwitchToLogin\n}) => {\n  _s();\n  const [formData, setFormData] = useState({\n    username: '',\n    email: '',\n    password: '',\n    confirmPassword: '',\n    firstName: '',\n    lastName: ''\n  });\n  const [loading, setLoading] = useState(false);\n  const [errors, setErrors] = useState({});\n  const handleChange = e => {\n    setFormData({\n      ...formData,\n      [e.target.name]: e.target.value\n    });\n\n    // Clear specific field error when user types\n    if (errors[e.target.name]) {\n      setErrors({\n        ...errors,\n        [e.target.name]: ''\n      });\n    }\n  };\n  const validateForm = () => {\n    const newErrors = {};\n    if (!formData.username.trim()) {\n      newErrors.username = 'Le nom d\\'utilisateur est requis';\n    } else if (formData.username.length < 3) {\n      newErrors.username = 'Le nom d\\'utilisateur doit contenir au moins 3 caractères';\n    }\n    if (!formData.email.trim()) {\n      newErrors.email = 'L\\'email est requis';\n    } else if (!/\\S+@\\S+\\.\\S+/.test(formData.email)) {\n      newErrors.email = 'Format d\\'email invalide';\n    }\n    if (!formData.password) {\n      newErrors.password = 'Le mot de passe est requis';\n    } else if (formData.password.length < 8) {\n      newErrors.password = 'Le mot de passe doit contenir au moins 8 caractères';\n    } else if (!/^(?=.*[a-z])(?=.*[A-Z])(?=.*\\d)(?=.*[@$!%*?&])[A-Za-z\\d@$!%*?&]/.test(formData.password)) {\n      newErrors.password = 'Le mot de passe doit contenir au moins une majuscule, une minuscule, un chiffre et un caractère spécial';\n    }\n    if (formData.password !== formData.confirmPassword) {\n      newErrors.confirmPassword = 'Les mots de passe ne correspondent pas';\n    }\n    if (!formData.firstName.trim()) {\n      newErrors.firstName = 'Le prénom est requis';\n    }\n    if (!formData.lastName.trim()) {\n      newErrors.lastName = 'Le nom est requis';\n    }\n    setErrors(newErrors);\n    return Object.keys(newErrors).length === 0;\n  };\n  const handleSubmit = async e => {\n    e.preventDefault();\n    if (!validateForm()) {\n      return;\n    }\n    setLoading(true);\n    try {\n      const response = await AuthService.register({\n        username: formData.username,\n        email: formData.email,\n        password: formData.password,\n        firstName: formData.firstName,\n        lastName: formData.lastName\n      });\n      if (response.success) {\n        // Store tokens\n        localStorage.setItem('token', response.data.token);\n        localStorage.setItem('refreshToken', response.data.refreshToken);\n        localStorage.setItem('user', JSON.stringify(response.data.user));\n\n        // Call parent callback\n        onRegister(response.data.user);\n      } else {\n        if (response.errors) {\n          const fieldErrors = {};\n          response.errors.forEach(error => {\n            fieldErrors[error.path] = error.msg;\n          });\n          setErrors(fieldErrors);\n        } else {\n          setErrors({\n            general: response.message || 'Registration failed'\n          });\n        }\n      }\n    } catch (error) {\n      var _error$response, _error$response$data;\n      console.error('Registration error:', error);\n      const errorMessage = ((_error$response = error.response) === null || _error$response === void 0 ? void 0 : (_error$response$data = _error$response.data) === null || _error$response$data === void 0 ? void 0 : _error$response$data.message) || 'Registration failed. Please try again.';\n      setErrors({\n        general: errorMessage\n      });\n    } finally {\n      setLoading(false);\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"card\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"card-header\",\n      children: /*#__PURE__*/_jsxDEV(\"h4\", {\n        className: \"mb-0\",\n        children: \"Cr\\xE9er un compte\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 119,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 118,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"card-body\",\n      children: [errors.general && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"alert alert-danger\",\n        role: \"alert\",\n        children: errors.general\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 123,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n        onSubmit: handleSubmit,\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"row\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"col-md-6 mb-3\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"firstName\",\n              className: \"form-label\",\n              children: \"Pr\\xE9nom *\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 131,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\",\n              className: `form-control ${errors.firstName ? 'is-invalid' : ''}`,\n              id: \"firstName\",\n              name: \"firstName\",\n              value: formData.firstName,\n              onChange: handleChange,\n              disabled: loading,\n              placeholder: \"Votre pr\\xE9nom\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 134,\n              columnNumber: 15\n            }, this), errors.firstName && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"invalid-feedback\",\n              children: errors.firstName\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 145,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 130,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"col-md-6 mb-3\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"lastName\",\n              className: \"form-label\",\n              children: \"Nom *\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 150,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\",\n              className: `form-control ${errors.lastName ? 'is-invalid' : ''}`,\n              id: \"lastName\",\n              name: \"lastName\",\n              value: formData.lastName,\n              onChange: handleChange,\n              disabled: loading,\n              placeholder: \"Votre nom\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 153,\n              columnNumber: 15\n            }, this), errors.lastName && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"invalid-feedback\",\n              children: errors.lastName\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 164,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 149,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 129,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mb-3\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            htmlFor: \"username\",\n            className: \"form-label\",\n            children: \"Nom d'utilisateur *\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 170,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"text\",\n            className: `form-control ${errors.username ? 'is-invalid' : ''}`,\n            id: \"username\",\n            name: \"username\",\n            value: formData.username,\n            onChange: handleChange,\n            disabled: loading,\n            placeholder: \"Choisissez un nom d'utilisateur\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 173,\n            columnNumber: 13\n          }, this), errors.username && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"invalid-feedback\",\n            children: errors.username\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 184,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 169,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mb-3\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            htmlFor: \"email\",\n            className: \"form-label\",\n            children: \"Email *\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 189,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"email\",\n            className: `form-control ${errors.email ? 'is-invalid' : ''}`,\n            id: \"email\",\n            name: \"email\",\n            value: formData.email,\n            onChange: handleChange,\n            disabled: loading,\n            placeholder: \"<EMAIL>\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 192,\n            columnNumber: 13\n          }, this), errors.email && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"invalid-feedback\",\n            children: errors.email\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 203,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 188,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mb-3\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            htmlFor: \"password\",\n            className: \"form-label\",\n            children: \"Mot de passe *\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 208,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"password\",\n            className: `form-control ${errors.password ? 'is-invalid' : ''}`,\n            id: \"password\",\n            name: \"password\",\n            value: formData.password,\n            onChange: handleChange,\n            disabled: loading,\n            placeholder: \"Minimum 8 caract\\xE8res\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 211,\n            columnNumber: 13\n          }, this), errors.password && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"invalid-feedback\",\n            children: errors.password\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 222,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 207,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mb-3\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            htmlFor: \"confirmPassword\",\n            className: \"form-label\",\n            children: \"Confirmer le mot de passe *\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 227,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"password\",\n            className: `form-control ${errors.confirmPassword ? 'is-invalid' : ''}`,\n            id: \"confirmPassword\",\n            name: \"confirmPassword\",\n            value: formData.confirmPassword,\n            onChange: handleChange,\n            disabled: loading,\n            placeholder: \"R\\xE9p\\xE9tez votre mot de passe\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 230,\n            columnNumber: 13\n          }, this), errors.confirmPassword && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"invalid-feedback\",\n            children: errors.confirmPassword\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 241,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 226,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"d-grid gap-2\",\n          children: /*#__PURE__*/_jsxDEV(\"button\", {\n            type: \"submit\",\n            className: \"btn btn-primary\",\n            disabled: loading,\n            children: loading ? /*#__PURE__*/_jsxDEV(_Fragment, {\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"spinner-border spinner-border-sm me-2\",\n                role: \"status\",\n                \"aria-hidden\": \"true\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 253,\n                columnNumber: 19\n              }, this), \"Cr\\xE9ation...\"]\n            }, void 0, true) : 'Créer le compte'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 246,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 245,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 128,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center mt-3\",\n        children: /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"mb-0\",\n          children: [\"D\\xE9j\\xE0 un compte ?\", ' ', /*#__PURE__*/_jsxDEV(\"button\", {\n            type: \"button\",\n            className: \"btn btn-link p-0\",\n            onClick: onSwitchToLogin,\n            disabled: loading,\n            children: \"Se connecter\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 266,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 264,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 263,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 121,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 117,\n    columnNumber: 5\n  }, this);\n};\n_s(Register, \"JCv6GK1taImJeqNjOwE4zkL+DfA=\");\n_c = Register;\nexport default Register;\nvar _c;\n$RefreshReg$(_c, \"Register\");", "map": {"version": 3, "names": ["React", "useState", "AuthService", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "Register", "onRegister", "onSwitchToLogin", "_s", "formData", "setFormData", "username", "email", "password", "confirmPassword", "firstName", "lastName", "loading", "setLoading", "errors", "setErrors", "handleChange", "e", "target", "name", "value", "validateForm", "newErrors", "trim", "length", "test", "Object", "keys", "handleSubmit", "preventDefault", "response", "register", "success", "localStorage", "setItem", "data", "token", "refreshToken", "JSON", "stringify", "user", "fieldErrors", "for<PERSON>ach", "error", "path", "msg", "general", "message", "_error$response", "_error$response$data", "console", "errorMessage", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "role", "onSubmit", "htmlFor", "type", "id", "onChange", "disabled", "placeholder", "onClick", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Desktop/Projrt FINOPS/frontend/src/components/Auth/Register.js"], "sourcesContent": ["import React, { useState } from 'react';\nimport AuthService from '../../services/AuthService';\n\nconst Register = ({ onRegister, onSwitchToLogin }) => {\n  const [formData, setFormData] = useState({\n    username: '',\n    email: '',\n    password: '',\n    confirmPassword: '',\n    firstName: '',\n    lastName: ''\n  });\n  const [loading, setLoading] = useState(false);\n  const [errors, setErrors] = useState({});\n\n  const handleChange = (e) => {\n    setFormData({\n      ...formData,\n      [e.target.name]: e.target.value\n    });\n    \n    // Clear specific field error when user types\n    if (errors[e.target.name]) {\n      setErrors({\n        ...errors,\n        [e.target.name]: ''\n      });\n    }\n  };\n\n  const validateForm = () => {\n    const newErrors = {};\n\n    if (!formData.username.trim()) {\n      newErrors.username = 'Le nom d\\'utilisateur est requis';\n    } else if (formData.username.length < 3) {\n      newErrors.username = 'Le nom d\\'utilisateur doit contenir au moins 3 caractères';\n    }\n\n    if (!formData.email.trim()) {\n      newErrors.email = 'L\\'email est requis';\n    } else if (!/\\S+@\\S+\\.\\S+/.test(formData.email)) {\n      newErrors.email = 'Format d\\'email invalide';\n    }\n\n    if (!formData.password) {\n      newErrors.password = 'Le mot de passe est requis';\n    } else if (formData.password.length < 8) {\n      newErrors.password = 'Le mot de passe doit contenir au moins 8 caractères';\n    } else if (!/^(?=.*[a-z])(?=.*[A-Z])(?=.*\\d)(?=.*[@$!%*?&])[A-Za-z\\d@$!%*?&]/.test(formData.password)) {\n      newErrors.password = 'Le mot de passe doit contenir au moins une majuscule, une minuscule, un chiffre et un caractère spécial';\n    }\n\n    if (formData.password !== formData.confirmPassword) {\n      newErrors.confirmPassword = 'Les mots de passe ne correspondent pas';\n    }\n\n    if (!formData.firstName.trim()) {\n      newErrors.firstName = 'Le prénom est requis';\n    }\n\n    if (!formData.lastName.trim()) {\n      newErrors.lastName = 'Le nom est requis';\n    }\n\n    setErrors(newErrors);\n    return Object.keys(newErrors).length === 0;\n  };\n\n  const handleSubmit = async (e) => {\n    e.preventDefault();\n    \n    if (!validateForm()) {\n      return;\n    }\n\n    setLoading(true);\n\n    try {\n      const response = await AuthService.register({\n        username: formData.username,\n        email: formData.email,\n        password: formData.password,\n        firstName: formData.firstName,\n        lastName: formData.lastName\n      });\n      \n      if (response.success) {\n        // Store tokens\n        localStorage.setItem('token', response.data.token);\n        localStorage.setItem('refreshToken', response.data.refreshToken);\n        localStorage.setItem('user', JSON.stringify(response.data.user));\n        \n        // Call parent callback\n        onRegister(response.data.user);\n      } else {\n        if (response.errors) {\n          const fieldErrors = {};\n          response.errors.forEach(error => {\n            fieldErrors[error.path] = error.msg;\n          });\n          setErrors(fieldErrors);\n        } else {\n          setErrors({ general: response.message || 'Registration failed' });\n        }\n      }\n    } catch (error) {\n      console.error('Registration error:', error);\n      const errorMessage = error.response?.data?.message || 'Registration failed. Please try again.';\n      setErrors({ general: errorMessage });\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  return (\n    <div className=\"card\">\n      <div className=\"card-header\">\n        <h4 className=\"mb-0\">Créer un compte</h4>\n      </div>\n      <div className=\"card-body\">\n        {errors.general && (\n          <div className=\"alert alert-danger\" role=\"alert\">\n            {errors.general}\n          </div>\n        )}\n        \n        <form onSubmit={handleSubmit}>\n          <div className=\"row\">\n            <div className=\"col-md-6 mb-3\">\n              <label htmlFor=\"firstName\" className=\"form-label\">\n                Prénom *\n              </label>\n              <input\n                type=\"text\"\n                className={`form-control ${errors.firstName ? 'is-invalid' : ''}`}\n                id=\"firstName\"\n                name=\"firstName\"\n                value={formData.firstName}\n                onChange={handleChange}\n                disabled={loading}\n                placeholder=\"Votre prénom\"\n              />\n              {errors.firstName && (\n                <div className=\"invalid-feedback\">{errors.firstName}</div>\n              )}\n            </div>\n            \n            <div className=\"col-md-6 mb-3\">\n              <label htmlFor=\"lastName\" className=\"form-label\">\n                Nom *\n              </label>\n              <input\n                type=\"text\"\n                className={`form-control ${errors.lastName ? 'is-invalid' : ''}`}\n                id=\"lastName\"\n                name=\"lastName\"\n                value={formData.lastName}\n                onChange={handleChange}\n                disabled={loading}\n                placeholder=\"Votre nom\"\n              />\n              {errors.lastName && (\n                <div className=\"invalid-feedback\">{errors.lastName}</div>\n              )}\n            </div>\n          </div>\n          \n          <div className=\"mb-3\">\n            <label htmlFor=\"username\" className=\"form-label\">\n              Nom d'utilisateur *\n            </label>\n            <input\n              type=\"text\"\n              className={`form-control ${errors.username ? 'is-invalid' : ''}`}\n              id=\"username\"\n              name=\"username\"\n              value={formData.username}\n              onChange={handleChange}\n              disabled={loading}\n              placeholder=\"Choisissez un nom d'utilisateur\"\n            />\n            {errors.username && (\n              <div className=\"invalid-feedback\">{errors.username}</div>\n            )}\n          </div>\n          \n          <div className=\"mb-3\">\n            <label htmlFor=\"email\" className=\"form-label\">\n              Email *\n            </label>\n            <input\n              type=\"email\"\n              className={`form-control ${errors.email ? 'is-invalid' : ''}`}\n              id=\"email\"\n              name=\"email\"\n              value={formData.email}\n              onChange={handleChange}\n              disabled={loading}\n              placeholder=\"<EMAIL>\"\n            />\n            {errors.email && (\n              <div className=\"invalid-feedback\">{errors.email}</div>\n            )}\n          </div>\n          \n          <div className=\"mb-3\">\n            <label htmlFor=\"password\" className=\"form-label\">\n              Mot de passe *\n            </label>\n            <input\n              type=\"password\"\n              className={`form-control ${errors.password ? 'is-invalid' : ''}`}\n              id=\"password\"\n              name=\"password\"\n              value={formData.password}\n              onChange={handleChange}\n              disabled={loading}\n              placeholder=\"Minimum 8 caractères\"\n            />\n            {errors.password && (\n              <div className=\"invalid-feedback\">{errors.password}</div>\n            )}\n          </div>\n          \n          <div className=\"mb-3\">\n            <label htmlFor=\"confirmPassword\" className=\"form-label\">\n              Confirmer le mot de passe *\n            </label>\n            <input\n              type=\"password\"\n              className={`form-control ${errors.confirmPassword ? 'is-invalid' : ''}`}\n              id=\"confirmPassword\"\n              name=\"confirmPassword\"\n              value={formData.confirmPassword}\n              onChange={handleChange}\n              disabled={loading}\n              placeholder=\"Répétez votre mot de passe\"\n            />\n            {errors.confirmPassword && (\n              <div className=\"invalid-feedback\">{errors.confirmPassword}</div>\n            )}\n          </div>\n          \n          <div className=\"d-grid gap-2\">\n            <button\n              type=\"submit\"\n              className=\"btn btn-primary\"\n              disabled={loading}\n            >\n              {loading ? (\n                <>\n                  <span className=\"spinner-border spinner-border-sm me-2\" role=\"status\" aria-hidden=\"true\"></span>\n                  Création...\n                </>\n              ) : (\n                'Créer le compte'\n              )}\n            </button>\n          </div>\n        </form>\n        \n        <div className=\"text-center mt-3\">\n          <p className=\"mb-0\">\n            Déjà un compte ?{' '}\n            <button\n              type=\"button\"\n              className=\"btn btn-link p-0\"\n              onClick={onSwitchToLogin}\n              disabled={loading}\n            >\n              Se connecter\n            </button>\n          </p>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default Register;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,OAAOC,WAAW,MAAM,4BAA4B;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAErD,MAAMC,QAAQ,GAAGA,CAAC;EAAEC,UAAU;EAAEC;AAAgB,CAAC,KAAK;EAAAC,EAAA;EACpD,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGX,QAAQ,CAAC;IACvCY,QAAQ,EAAE,EAAE;IACZC,KAAK,EAAE,EAAE;IACTC,QAAQ,EAAE,EAAE;IACZC,eAAe,EAAE,EAAE;IACnBC,SAAS,EAAE,EAAE;IACbC,QAAQ,EAAE;EACZ,CAAC,CAAC;EACF,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGnB,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACoB,MAAM,EAAEC,SAAS,CAAC,GAAGrB,QAAQ,CAAC,CAAC,CAAC,CAAC;EAExC,MAAMsB,YAAY,GAAIC,CAAC,IAAK;IAC1BZ,WAAW,CAAC;MACV,GAAGD,QAAQ;MACX,CAACa,CAAC,CAACC,MAAM,CAACC,IAAI,GAAGF,CAAC,CAACC,MAAM,CAACE;IAC5B,CAAC,CAAC;;IAEF;IACA,IAAIN,MAAM,CAACG,CAAC,CAACC,MAAM,CAACC,IAAI,CAAC,EAAE;MACzBJ,SAAS,CAAC;QACR,GAAGD,MAAM;QACT,CAACG,CAAC,CAACC,MAAM,CAACC,IAAI,GAAG;MACnB,CAAC,CAAC;IACJ;EACF,CAAC;EAED,MAAME,YAAY,GAAGA,CAAA,KAAM;IACzB,MAAMC,SAAS,GAAG,CAAC,CAAC;IAEpB,IAAI,CAAClB,QAAQ,CAACE,QAAQ,CAACiB,IAAI,CAAC,CAAC,EAAE;MAC7BD,SAAS,CAAChB,QAAQ,GAAG,kCAAkC;IACzD,CAAC,MAAM,IAAIF,QAAQ,CAACE,QAAQ,CAACkB,MAAM,GAAG,CAAC,EAAE;MACvCF,SAAS,CAAChB,QAAQ,GAAG,2DAA2D;IAClF;IAEA,IAAI,CAACF,QAAQ,CAACG,KAAK,CAACgB,IAAI,CAAC,CAAC,EAAE;MAC1BD,SAAS,CAACf,KAAK,GAAG,qBAAqB;IACzC,CAAC,MAAM,IAAI,CAAC,cAAc,CAACkB,IAAI,CAACrB,QAAQ,CAACG,KAAK,CAAC,EAAE;MAC/Ce,SAAS,CAACf,KAAK,GAAG,0BAA0B;IAC9C;IAEA,IAAI,CAACH,QAAQ,CAACI,QAAQ,EAAE;MACtBc,SAAS,CAACd,QAAQ,GAAG,4BAA4B;IACnD,CAAC,MAAM,IAAIJ,QAAQ,CAACI,QAAQ,CAACgB,MAAM,GAAG,CAAC,EAAE;MACvCF,SAAS,CAACd,QAAQ,GAAG,qDAAqD;IAC5E,CAAC,MAAM,IAAI,CAAC,iEAAiE,CAACiB,IAAI,CAACrB,QAAQ,CAACI,QAAQ,CAAC,EAAE;MACrGc,SAAS,CAACd,QAAQ,GAAG,yGAAyG;IAChI;IAEA,IAAIJ,QAAQ,CAACI,QAAQ,KAAKJ,QAAQ,CAACK,eAAe,EAAE;MAClDa,SAAS,CAACb,eAAe,GAAG,wCAAwC;IACtE;IAEA,IAAI,CAACL,QAAQ,CAACM,SAAS,CAACa,IAAI,CAAC,CAAC,EAAE;MAC9BD,SAAS,CAACZ,SAAS,GAAG,sBAAsB;IAC9C;IAEA,IAAI,CAACN,QAAQ,CAACO,QAAQ,CAACY,IAAI,CAAC,CAAC,EAAE;MAC7BD,SAAS,CAACX,QAAQ,GAAG,mBAAmB;IAC1C;IAEAI,SAAS,CAACO,SAAS,CAAC;IACpB,OAAOI,MAAM,CAACC,IAAI,CAACL,SAAS,CAAC,CAACE,MAAM,KAAK,CAAC;EAC5C,CAAC;EAED,MAAMI,YAAY,GAAG,MAAOX,CAAC,IAAK;IAChCA,CAAC,CAACY,cAAc,CAAC,CAAC;IAElB,IAAI,CAACR,YAAY,CAAC,CAAC,EAAE;MACnB;IACF;IAEAR,UAAU,CAAC,IAAI,CAAC;IAEhB,IAAI;MACF,MAAMiB,QAAQ,GAAG,MAAMnC,WAAW,CAACoC,QAAQ,CAAC;QAC1CzB,QAAQ,EAAEF,QAAQ,CAACE,QAAQ;QAC3BC,KAAK,EAAEH,QAAQ,CAACG,KAAK;QACrBC,QAAQ,EAAEJ,QAAQ,CAACI,QAAQ;QAC3BE,SAAS,EAAEN,QAAQ,CAACM,SAAS;QAC7BC,QAAQ,EAAEP,QAAQ,CAACO;MACrB,CAAC,CAAC;MAEF,IAAImB,QAAQ,CAACE,OAAO,EAAE;QACpB;QACAC,YAAY,CAACC,OAAO,CAAC,OAAO,EAAEJ,QAAQ,CAACK,IAAI,CAACC,KAAK,CAAC;QAClDH,YAAY,CAACC,OAAO,CAAC,cAAc,EAAEJ,QAAQ,CAACK,IAAI,CAACE,YAAY,CAAC;QAChEJ,YAAY,CAACC,OAAO,CAAC,MAAM,EAAEI,IAAI,CAACC,SAAS,CAACT,QAAQ,CAACK,IAAI,CAACK,IAAI,CAAC,CAAC;;QAEhE;QACAvC,UAAU,CAAC6B,QAAQ,CAACK,IAAI,CAACK,IAAI,CAAC;MAChC,CAAC,MAAM;QACL,IAAIV,QAAQ,CAAChB,MAAM,EAAE;UACnB,MAAM2B,WAAW,GAAG,CAAC,CAAC;UACtBX,QAAQ,CAAChB,MAAM,CAAC4B,OAAO,CAACC,KAAK,IAAI;YAC/BF,WAAW,CAACE,KAAK,CAACC,IAAI,CAAC,GAAGD,KAAK,CAACE,GAAG;UACrC,CAAC,CAAC;UACF9B,SAAS,CAAC0B,WAAW,CAAC;QACxB,CAAC,MAAM;UACL1B,SAAS,CAAC;YAAE+B,OAAO,EAAEhB,QAAQ,CAACiB,OAAO,IAAI;UAAsB,CAAC,CAAC;QACnE;MACF;IACF,CAAC,CAAC,OAAOJ,KAAK,EAAE;MAAA,IAAAK,eAAA,EAAAC,oBAAA;MACdC,OAAO,CAACP,KAAK,CAAC,qBAAqB,EAAEA,KAAK,CAAC;MAC3C,MAAMQ,YAAY,GAAG,EAAAH,eAAA,GAAAL,KAAK,CAACb,QAAQ,cAAAkB,eAAA,wBAAAC,oBAAA,GAAdD,eAAA,CAAgBb,IAAI,cAAAc,oBAAA,uBAApBA,oBAAA,CAAsBF,OAAO,KAAI,wCAAwC;MAC9FhC,SAAS,CAAC;QAAE+B,OAAO,EAAEK;MAAa,CAAC,CAAC;IACtC,CAAC,SAAS;MACRtC,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,oBACEhB,OAAA;IAAKuD,SAAS,EAAC,MAAM;IAAAC,QAAA,gBACnBxD,OAAA;MAAKuD,SAAS,EAAC,aAAa;MAAAC,QAAA,eAC1BxD,OAAA;QAAIuD,SAAS,EAAC,MAAM;QAAAC,QAAA,EAAC;MAAe;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACtC,CAAC,eACN5D,OAAA;MAAKuD,SAAS,EAAC,WAAW;MAAAC,QAAA,GACvBvC,MAAM,CAACgC,OAAO,iBACbjD,OAAA;QAAKuD,SAAS,EAAC,oBAAoB;QAACM,IAAI,EAAC,OAAO;QAAAL,QAAA,EAC7CvC,MAAM,CAACgC;MAAO;QAAAQ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACZ,CACN,eAED5D,OAAA;QAAM8D,QAAQ,EAAE/B,YAAa;QAAAyB,QAAA,gBAC3BxD,OAAA;UAAKuD,SAAS,EAAC,KAAK;UAAAC,QAAA,gBAClBxD,OAAA;YAAKuD,SAAS,EAAC,eAAe;YAAAC,QAAA,gBAC5BxD,OAAA;cAAO+D,OAAO,EAAC,WAAW;cAACR,SAAS,EAAC,YAAY;cAAAC,QAAA,EAAC;YAElD;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACR5D,OAAA;cACEgE,IAAI,EAAC,MAAM;cACXT,SAAS,EAAE,gBAAgBtC,MAAM,CAACJ,SAAS,GAAG,YAAY,GAAG,EAAE,EAAG;cAClEoD,EAAE,EAAC,WAAW;cACd3C,IAAI,EAAC,WAAW;cAChBC,KAAK,EAAEhB,QAAQ,CAACM,SAAU;cAC1BqD,QAAQ,EAAE/C,YAAa;cACvBgD,QAAQ,EAAEpD,OAAQ;cAClBqD,WAAW,EAAC;YAAc;cAAAX,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3B,CAAC,EACD3C,MAAM,CAACJ,SAAS,iBACfb,OAAA;cAAKuD,SAAS,EAAC,kBAAkB;cAAAC,QAAA,EAAEvC,MAAM,CAACJ;YAAS;cAAA4C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAC1D;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eAEN5D,OAAA;YAAKuD,SAAS,EAAC,eAAe;YAAAC,QAAA,gBAC5BxD,OAAA;cAAO+D,OAAO,EAAC,UAAU;cAACR,SAAS,EAAC,YAAY;cAAAC,QAAA,EAAC;YAEjD;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACR5D,OAAA;cACEgE,IAAI,EAAC,MAAM;cACXT,SAAS,EAAE,gBAAgBtC,MAAM,CAACH,QAAQ,GAAG,YAAY,GAAG,EAAE,EAAG;cACjEmD,EAAE,EAAC,UAAU;cACb3C,IAAI,EAAC,UAAU;cACfC,KAAK,EAAEhB,QAAQ,CAACO,QAAS;cACzBoD,QAAQ,EAAE/C,YAAa;cACvBgD,QAAQ,EAAEpD,OAAQ;cAClBqD,WAAW,EAAC;YAAW;cAAAX,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxB,CAAC,EACD3C,MAAM,CAACH,QAAQ,iBACdd,OAAA;cAAKuD,SAAS,EAAC,kBAAkB;cAAAC,QAAA,EAAEvC,MAAM,CAACH;YAAQ;cAAA2C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CACzD;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAEN5D,OAAA;UAAKuD,SAAS,EAAC,MAAM;UAAAC,QAAA,gBACnBxD,OAAA;YAAO+D,OAAO,EAAC,UAAU;YAACR,SAAS,EAAC,YAAY;YAAAC,QAAA,EAAC;UAEjD;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACR5D,OAAA;YACEgE,IAAI,EAAC,MAAM;YACXT,SAAS,EAAE,gBAAgBtC,MAAM,CAACR,QAAQ,GAAG,YAAY,GAAG,EAAE,EAAG;YACjEwD,EAAE,EAAC,UAAU;YACb3C,IAAI,EAAC,UAAU;YACfC,KAAK,EAAEhB,QAAQ,CAACE,QAAS;YACzByD,QAAQ,EAAE/C,YAAa;YACvBgD,QAAQ,EAAEpD,OAAQ;YAClBqD,WAAW,EAAC;UAAiC;YAAAX,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9C,CAAC,EACD3C,MAAM,CAACR,QAAQ,iBACdT,OAAA;YAAKuD,SAAS,EAAC,kBAAkB;YAAAC,QAAA,EAAEvC,MAAM,CAACR;UAAQ;YAAAgD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CACzD;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,eAEN5D,OAAA;UAAKuD,SAAS,EAAC,MAAM;UAAAC,QAAA,gBACnBxD,OAAA;YAAO+D,OAAO,EAAC,OAAO;YAACR,SAAS,EAAC,YAAY;YAAAC,QAAA,EAAC;UAE9C;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACR5D,OAAA;YACEgE,IAAI,EAAC,OAAO;YACZT,SAAS,EAAE,gBAAgBtC,MAAM,CAACP,KAAK,GAAG,YAAY,GAAG,EAAE,EAAG;YAC9DuD,EAAE,EAAC,OAAO;YACV3C,IAAI,EAAC,OAAO;YACZC,KAAK,EAAEhB,QAAQ,CAACG,KAAM;YACtBwD,QAAQ,EAAE/C,YAAa;YACvBgD,QAAQ,EAAEpD,OAAQ;YAClBqD,WAAW,EAAC;UAAiB;YAAAX,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9B,CAAC,EACD3C,MAAM,CAACP,KAAK,iBACXV,OAAA;YAAKuD,SAAS,EAAC,kBAAkB;YAAAC,QAAA,EAAEvC,MAAM,CAACP;UAAK;YAAA+C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CACtD;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,eAEN5D,OAAA;UAAKuD,SAAS,EAAC,MAAM;UAAAC,QAAA,gBACnBxD,OAAA;YAAO+D,OAAO,EAAC,UAAU;YAACR,SAAS,EAAC,YAAY;YAAAC,QAAA,EAAC;UAEjD;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACR5D,OAAA;YACEgE,IAAI,EAAC,UAAU;YACfT,SAAS,EAAE,gBAAgBtC,MAAM,CAACN,QAAQ,GAAG,YAAY,GAAG,EAAE,EAAG;YACjEsD,EAAE,EAAC,UAAU;YACb3C,IAAI,EAAC,UAAU;YACfC,KAAK,EAAEhB,QAAQ,CAACI,QAAS;YACzBuD,QAAQ,EAAE/C,YAAa;YACvBgD,QAAQ,EAAEpD,OAAQ;YAClBqD,WAAW,EAAC;UAAsB;YAAAX,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnC,CAAC,EACD3C,MAAM,CAACN,QAAQ,iBACdX,OAAA;YAAKuD,SAAS,EAAC,kBAAkB;YAAAC,QAAA,EAAEvC,MAAM,CAACN;UAAQ;YAAA8C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CACzD;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,eAEN5D,OAAA;UAAKuD,SAAS,EAAC,MAAM;UAAAC,QAAA,gBACnBxD,OAAA;YAAO+D,OAAO,EAAC,iBAAiB;YAACR,SAAS,EAAC,YAAY;YAAAC,QAAA,EAAC;UAExD;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACR5D,OAAA;YACEgE,IAAI,EAAC,UAAU;YACfT,SAAS,EAAE,gBAAgBtC,MAAM,CAACL,eAAe,GAAG,YAAY,GAAG,EAAE,EAAG;YACxEqD,EAAE,EAAC,iBAAiB;YACpB3C,IAAI,EAAC,iBAAiB;YACtBC,KAAK,EAAEhB,QAAQ,CAACK,eAAgB;YAChCsD,QAAQ,EAAE/C,YAAa;YACvBgD,QAAQ,EAAEpD,OAAQ;YAClBqD,WAAW,EAAC;UAA4B;YAAAX,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzC,CAAC,EACD3C,MAAM,CAACL,eAAe,iBACrBZ,OAAA;YAAKuD,SAAS,EAAC,kBAAkB;YAAAC,QAAA,EAAEvC,MAAM,CAACL;UAAe;YAAA6C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAChE;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,eAEN5D,OAAA;UAAKuD,SAAS,EAAC,cAAc;UAAAC,QAAA,eAC3BxD,OAAA;YACEgE,IAAI,EAAC,QAAQ;YACbT,SAAS,EAAC,iBAAiB;YAC3BY,QAAQ,EAAEpD,OAAQ;YAAAyC,QAAA,EAEjBzC,OAAO,gBACNf,OAAA,CAAAE,SAAA;cAAAsD,QAAA,gBACExD,OAAA;gBAAMuD,SAAS,EAAC,uCAAuC;gBAACM,IAAI,EAAC,QAAQ;gBAAC,eAAY;cAAM;gBAAAJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,kBAElG;YAAA,eAAE,CAAC,GAEH;UACD;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACK;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,eAEP5D,OAAA;QAAKuD,SAAS,EAAC,kBAAkB;QAAAC,QAAA,eAC/BxD,OAAA;UAAGuD,SAAS,EAAC,MAAM;UAAAC,QAAA,GAAC,wBACF,EAAC,GAAG,eACpBxD,OAAA;YACEgE,IAAI,EAAC,QAAQ;YACbT,SAAS,EAAC,kBAAkB;YAC5Bc,OAAO,EAAEhE,eAAgB;YACzB8D,QAAQ,EAAEpD,OAAQ;YAAAyC,QAAA,EACnB;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACtD,EAAA,CAnRIH,QAAQ;AAAAmE,EAAA,GAARnE,QAAQ;AAqRd,eAAeA,QAAQ;AAAC,IAAAmE,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
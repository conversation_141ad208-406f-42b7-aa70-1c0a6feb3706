{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Desktop/Projrt FINOPS/frontend/src/App.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';\nimport Login from './components/Auth/Login';\nimport Register from './components/Auth/Register';\nimport Dashboard from './components/Dashboard/Dashboard';\nimport CostAnalysis from './components/CostAnalysis/CostAnalysis';\nimport BudgetManagement from './components/Budget/BudgetManagement';\nimport Reports from './components/Reports/Reports';\nimport Navigation from './components/Navigation/Navigation';\nimport AuthService from './services/AuthService';\nimport ErrorBoundary from './components/ErrorBoundary/ErrorBoundary';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nfunction App() {\n  _s();\n  const [user, setUser] = useState(null);\n  const [loading, setLoading] = useState(true);\n  useEffect(() => {\n    const token = localStorage.getItem('token');\n    if (token) {\n      AuthService.getCurrentUser().then(userData => {\n        setUser(userData);\n        setLoading(false);\n      }).catch(() => {\n        localStorage.removeItem('token');\n        setLoading(false);\n      });\n    } else {\n      setLoading(false);\n    }\n  }, []);\n  const handleLogin = userData => {\n    setUser(userData);\n  };\n  const handleLogout = () => {\n    AuthService.logout();\n    setUser(null);\n  };\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"d-flex justify-content-center align-items-center\",\n      style: {\n        height: '100vh'\n      },\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"spinner-border text-primary\",\n        role: \"status\",\n        children: /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"visually-hidden\",\n          children: \"Loading...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 47,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 46,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 45,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(ErrorBoundary, {\n    children: /*#__PURE__*/_jsxDEV(Router, {\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"App\",\n        children: user ? /*#__PURE__*/_jsxDEV(_Fragment, {\n          children: [/*#__PURE__*/_jsxDEV(Navigation, {\n            user: user,\n            onLogout: handleLogout\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 59,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"container-fluid\",\n            children: /*#__PURE__*/_jsxDEV(Routes, {\n              children: [/*#__PURE__*/_jsxDEV(Route, {\n                path: \"/\",\n                element: /*#__PURE__*/_jsxDEV(Navigate, {\n                  to: \"/dashboard\",\n                  replace: true\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 62,\n                  columnNumber: 44\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 62,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Route, {\n                path: \"/dashboard\",\n                element: /*#__PURE__*/_jsxDEV(Dashboard, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 63,\n                  columnNumber: 53\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 63,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Route, {\n                path: \"/cost-analysis\",\n                element: /*#__PURE__*/_jsxDEV(CostAnalysis, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 64,\n                  columnNumber: 57\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 64,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Route, {\n                path: \"/budgets\",\n                element: /*#__PURE__*/_jsxDEV(BudgetManagement, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 65,\n                  columnNumber: 51\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 65,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Route, {\n                path: \"/reports\",\n                element: /*#__PURE__*/_jsxDEV(Reports, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 66,\n                  columnNumber: 51\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 66,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Route, {\n                path: \"*\",\n                element: /*#__PURE__*/_jsxDEV(Navigate, {\n                  to: \"/dashboard\",\n                  replace: true\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 67,\n                  columnNumber: 44\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 67,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 61,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 60,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true) : /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"container\",\n          children: /*#__PURE__*/_jsxDEV(Routes, {\n            children: [/*#__PURE__*/_jsxDEV(Route, {\n              path: \"/login\",\n              element: /*#__PURE__*/_jsxDEV(Login, {\n                onLogin: handleLogin\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 74,\n                columnNumber: 47\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 74,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Route, {\n              path: \"/register\",\n              element: /*#__PURE__*/_jsxDEV(Register, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 75,\n                columnNumber: 50\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 75,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Route, {\n              path: \"*\",\n              element: /*#__PURE__*/_jsxDEV(Navigate, {\n                to: \"/login\",\n                replace: true\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 76,\n                columnNumber: 42\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 76,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 73,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 72,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 56,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 55,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 54,\n    columnNumber: 5\n  }, this);\n}\n_s(App, \"NiO5z6JIqzX62LS5UWDgIqbZYyY=\");\n_c = App;\nexport default App;\nvar _c;\n$RefreshReg$(_c, \"App\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Router", "Routes", "Route", "Navigate", "<PERSON><PERSON>", "Register", "Dashboard", "CostAnalysis", "BudgetManagement", "Reports", "Navigation", "AuthService", "Error<PERSON>ou<PERSON><PERSON>", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "App", "_s", "user", "setUser", "loading", "setLoading", "token", "localStorage", "getItem", "getCurrentUser", "then", "userData", "catch", "removeItem", "handleLogin", "handleLogout", "logout", "className", "style", "height", "children", "role", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onLogout", "path", "element", "to", "replace", "onLogin", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Desktop/Projrt FINOPS/frontend/src/App.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';\nimport Login from './components/Auth/Login';\nimport Register from './components/Auth/Register';\nimport Dashboard from './components/Dashboard/Dashboard';\nimport CostAnalysis from './components/CostAnalysis/CostAnalysis';\nimport BudgetManagement from './components/Budget/BudgetManagement';\nimport Reports from './components/Reports/Reports';\nimport Navigation from './components/Navigation/Navigation';\nimport AuthService from './services/AuthService';\nimport ErrorBoundary from './components/ErrorBoundary/ErrorBoundary';\n\nfunction App() {\n  const [user, setUser] = useState(null);\n  const [loading, setLoading] = useState(true);\n\n  useEffect(() => {\n    const token = localStorage.getItem('token');\n    if (token) {\n      AuthService.getCurrentUser()\n        .then(userData => {\n          setUser(userData);\n          setLoading(false);\n        })\n        .catch(() => {\n          localStorage.removeItem('token');\n          setLoading(false);\n        });\n    } else {\n      setLoading(false);\n    }\n  }, []);\n\n  const handleLogin = (userData) => {\n    setUser(userData);\n  };\n\n  const handleLogout = () => {\n    AuthService.logout();\n    setUser(null);\n  };\n\n  if (loading) {\n    return (\n      <div className=\"d-flex justify-content-center align-items-center\" style={{ height: '100vh' }}>\n        <div className=\"spinner-border text-primary\" role=\"status\">\n          <span className=\"visually-hidden\">Loading...</span>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <ErrorBoundary>\n      <Router>\n        <div className=\"App\">\n          {user ? (\n            <>\n              <Navigation user={user} onLogout={handleLogout} />\n              <div className=\"container-fluid\">\n                <Routes>\n                  <Route path=\"/\" element={<Navigate to=\"/dashboard\" replace />} />\n                  <Route path=\"/dashboard\" element={<Dashboard />} />\n                  <Route path=\"/cost-analysis\" element={<CostAnalysis />} />\n                  <Route path=\"/budgets\" element={<BudgetManagement />} />\n                  <Route path=\"/reports\" element={<Reports />} />\n                  <Route path=\"*\" element={<Navigate to=\"/dashboard\" replace />} />\n                </Routes>\n              </div>\n            </>\n          ) : (\n            <div className=\"container\">\n              <Routes>\n                <Route path=\"/login\" element={<Login onLogin={handleLogin} />} />\n                <Route path=\"/register\" element={<Register />} />\n                <Route path=\"*\" element={<Navigate to=\"/login\" replace />} />\n              </Routes>\n            </div>\n          )}\n        </div>\n      </Router>\n    </ErrorBoundary>\n  );\n}\n\nexport default App;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,aAAa,IAAIC,MAAM,EAAEC,MAAM,EAAEC,KAAK,EAAEC,QAAQ,QAAQ,kBAAkB;AACnF,OAAOC,KAAK,MAAM,yBAAyB;AAC3C,OAAOC,QAAQ,MAAM,4BAA4B;AACjD,OAAOC,SAAS,MAAM,kCAAkC;AACxD,OAAOC,YAAY,MAAM,wCAAwC;AACjE,OAAOC,gBAAgB,MAAM,sCAAsC;AACnE,OAAOC,OAAO,MAAM,8BAA8B;AAClD,OAAOC,UAAU,MAAM,oCAAoC;AAC3D,OAAOC,WAAW,MAAM,wBAAwB;AAChD,OAAOC,aAAa,MAAM,0CAA0C;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAErE,SAASC,GAAGA,CAAA,EAAG;EAAAC,EAAA;EACb,MAAM,CAACC,IAAI,EAAEC,OAAO,CAAC,GAAGvB,QAAQ,CAAC,IAAI,CAAC;EACtC,MAAM,CAACwB,OAAO,EAAEC,UAAU,CAAC,GAAGzB,QAAQ,CAAC,IAAI,CAAC;EAE5CC,SAAS,CAAC,MAAM;IACd,MAAMyB,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;IAC3C,IAAIF,KAAK,EAAE;MACTZ,WAAW,CAACe,cAAc,CAAC,CAAC,CACzBC,IAAI,CAACC,QAAQ,IAAI;QAChBR,OAAO,CAACQ,QAAQ,CAAC;QACjBN,UAAU,CAAC,KAAK,CAAC;MACnB,CAAC,CAAC,CACDO,KAAK,CAAC,MAAM;QACXL,YAAY,CAACM,UAAU,CAAC,OAAO,CAAC;QAChCR,UAAU,CAAC,KAAK,CAAC;MACnB,CAAC,CAAC;IACN,CAAC,MAAM;MACLA,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMS,WAAW,GAAIH,QAAQ,IAAK;IAChCR,OAAO,CAACQ,QAAQ,CAAC;EACnB,CAAC;EAED,MAAMI,YAAY,GAAGA,CAAA,KAAM;IACzBrB,WAAW,CAACsB,MAAM,CAAC,CAAC;IACpBb,OAAO,CAAC,IAAI,CAAC;EACf,CAAC;EAED,IAAIC,OAAO,EAAE;IACX,oBACEP,OAAA;MAAKoB,SAAS,EAAC,kDAAkD;MAACC,KAAK,EAAE;QAAEC,MAAM,EAAE;MAAQ,CAAE;MAAAC,QAAA,eAC3FvB,OAAA;QAAKoB,SAAS,EAAC,6BAA6B;QAACI,IAAI,EAAC,QAAQ;QAAAD,QAAA,eACxDvB,OAAA;UAAMoB,SAAS,EAAC,iBAAiB;UAAAG,QAAA,EAAC;QAAU;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAChD;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,oBACE5B,OAAA,CAACF,aAAa;IAAAyB,QAAA,eACZvB,OAAA,CAACd,MAAM;MAAAqC,QAAA,eACLvB,OAAA;QAAKoB,SAAS,EAAC,KAAK;QAAAG,QAAA,EACjBlB,IAAI,gBACHL,OAAA,CAAAE,SAAA;UAAAqB,QAAA,gBACEvB,OAAA,CAACJ,UAAU;YAACS,IAAI,EAAEA,IAAK;YAACwB,QAAQ,EAAEX;UAAa;YAAAO,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAClD5B,OAAA;YAAKoB,SAAS,EAAC,iBAAiB;YAAAG,QAAA,eAC9BvB,OAAA,CAACb,MAAM;cAAAoC,QAAA,gBACLvB,OAAA,CAACZ,KAAK;gBAAC0C,IAAI,EAAC,GAAG;gBAACC,OAAO,eAAE/B,OAAA,CAACX,QAAQ;kBAAC2C,EAAE,EAAC,YAAY;kBAACC,OAAO;gBAAA;kBAAAR,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAE;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACjE5B,OAAA,CAACZ,KAAK;gBAAC0C,IAAI,EAAC,YAAY;gBAACC,OAAO,eAAE/B,OAAA,CAACR,SAAS;kBAAAiC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAE;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACnD5B,OAAA,CAACZ,KAAK;gBAAC0C,IAAI,EAAC,gBAAgB;gBAACC,OAAO,eAAE/B,OAAA,CAACP,YAAY;kBAAAgC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAE;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAC1D5B,OAAA,CAACZ,KAAK;gBAAC0C,IAAI,EAAC,UAAU;gBAACC,OAAO,eAAE/B,OAAA,CAACN,gBAAgB;kBAAA+B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAE;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACxD5B,OAAA,CAACZ,KAAK;gBAAC0C,IAAI,EAAC,UAAU;gBAACC,OAAO,eAAE/B,OAAA,CAACL,OAAO;kBAAA8B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAE;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAC/C5B,OAAA,CAACZ,KAAK;gBAAC0C,IAAI,EAAC,GAAG;gBAACC,OAAO,eAAE/B,OAAA,CAACX,QAAQ;kBAAC2C,EAAE,EAAC,YAAY;kBAACC,OAAO;gBAAA;kBAAAR,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAE;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3D;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA,eACN,CAAC,gBAEH5B,OAAA;UAAKoB,SAAS,EAAC,WAAW;UAAAG,QAAA,eACxBvB,OAAA,CAACb,MAAM;YAAAoC,QAAA,gBACLvB,OAAA,CAACZ,KAAK;cAAC0C,IAAI,EAAC,QAAQ;cAACC,OAAO,eAAE/B,OAAA,CAACV,KAAK;gBAAC4C,OAAO,EAAEjB;cAAY;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAE;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACjE5B,OAAA,CAACZ,KAAK;cAAC0C,IAAI,EAAC,WAAW;cAACC,OAAO,eAAE/B,OAAA,CAACT,QAAQ;gBAAAkC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAE;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACjD5B,OAAA,CAACZ,KAAK;cAAC0C,IAAI,EAAC,GAAG;cAACC,OAAO,eAAE/B,OAAA,CAACX,QAAQ;gBAAC2C,EAAE,EAAC,QAAQ;gBAACC,OAAO;cAAA;gBAAAR,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAE;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvD;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN;MACN;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACI,CAAC;AAEpB;AAACxB,EAAA,CAvEQD,GAAG;AAAAgC,EAAA,GAAHhC,GAAG;AAyEZ,eAAeA,GAAG;AAAC,IAAAgC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
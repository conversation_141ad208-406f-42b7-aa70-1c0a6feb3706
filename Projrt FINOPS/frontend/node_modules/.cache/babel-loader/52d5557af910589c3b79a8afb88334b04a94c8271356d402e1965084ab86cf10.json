{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Desktop/Projrt FINOPS/frontend/src/components/ErrorBoundary/ErrorBoundary.js\",\n  _s = $RefreshSig$();\nimport React from 'react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nclass ErrorBoundary extends React.Component {\n  constructor(props) {\n    super(props);\n    this.logErrorToService = (error, errorInfo, errorId) => {\n      try {\n        // In a real application, you would send this to your logging service\n        const errorData = {\n          errorId,\n          message: error.message,\n          stack: error.stack,\n          componentStack: errorInfo.componentStack,\n          timestamp: new Date().toISOString(),\n          userAgent: navigator.userAgent,\n          url: window.location.href,\n          userId: localStorage.getItem('user') ? JSON.parse(localStorage.getItem('user')).id : null\n        };\n\n        // Example: Send to your API\n        // fetch('/api/errors', {\n        //   method: 'POST',\n        //   headers: { 'Content-Type': 'application/json' },\n        //   body: JSON.stringify(errorData)\n        // });\n\n        console.log('Error logged:', errorData);\n      } catch (loggingError) {\n        console.error('Failed to log error:', loggingError);\n      }\n    };\n    this.handleRetry = () => {\n      this.setState({\n        hasError: false,\n        error: null,\n        errorInfo: null,\n        errorId: null\n      });\n    };\n    this.handleReload = () => {\n      window.location.reload();\n    };\n    this.state = {\n      hasError: false,\n      error: null,\n      errorInfo: null,\n      errorId: null\n    };\n  }\n  static getDerivedStateFromError(error) {\n    // Update state so the next render will show the fallback UI\n    return {\n      hasError: true\n    };\n  }\n  componentDidCatch(error, errorInfo) {\n    // Generate unique error ID for tracking\n    const errorId = `error_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;\n\n    // Log error details\n    console.error('ErrorBoundary caught an error:', error, errorInfo);\n\n    // Update state with error details\n    this.setState({\n      error,\n      errorInfo,\n      errorId\n    });\n\n    // Send error to logging service (if available)\n    this.logErrorToService(error, errorInfo, errorId);\n  }\n  render() {\n    if (this.state.hasError) {\n      var _this$state$error, _this$state$error2, _this$state$errorInfo;\n      // Custom fallback UI\n      if (this.props.fallback) {\n        return this.props.fallback(this.state.error, this.handleRetry);\n      }\n\n      // Default fallback UI\n      return /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"error-boundary\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"container mt-5\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"row justify-content-center\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"col-md-8\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"card border-danger\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"card-header bg-danger text-white\",\n                  children: /*#__PURE__*/_jsxDEV(\"h4\", {\n                    className: \"mb-0\",\n                    children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                      className: \"fas fa-exclamation-triangle me-2\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 93,\n                      columnNumber: 23\n                    }, this), \"Une erreur s'est produite\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 92,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 91,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"card-body\",\n                  children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"card-text\",\n                    children: \"D\\xE9sol\\xE9, quelque chose s'est mal pass\\xE9. L'erreur a \\xE9t\\xE9 signal\\xE9e \\xE0 notre \\xE9quipe.\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 98,\n                    columnNumber: 21\n                  }, this), process.env.NODE_ENV === 'development' && /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"mt-3\",\n                    children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n                      children: \"D\\xE9tails de l'erreur (mode d\\xE9veloppement):\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 104,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"alert alert-secondary\",\n                      children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                        children: \"Message:\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 106,\n                        columnNumber: 27\n                      }, this), \" \", (_this$state$error = this.state.error) === null || _this$state$error === void 0 ? void 0 : _this$state$error.message, /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 107,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"strong\", {\n                        children: \"ID d'erreur:\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 108,\n                        columnNumber: 27\n                      }, this), \" \", this.state.errorId]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 105,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"details\", {\n                      className: \"mt-2\",\n                      children: [/*#__PURE__*/_jsxDEV(\"summary\", {\n                        className: \"btn btn-outline-secondary btn-sm\",\n                        children: \"Voir la stack trace\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 112,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"pre\", {\n                        className: \"mt-2 p-2 bg-light border rounded\",\n                        style: {\n                          fontSize: '0.8rem'\n                        },\n                        children: (_this$state$error2 = this.state.error) === null || _this$state$error2 === void 0 ? void 0 : _this$state$error2.stack\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 115,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 111,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"details\", {\n                      className: \"mt-2\",\n                      children: [/*#__PURE__*/_jsxDEV(\"summary\", {\n                        className: \"btn btn-outline-secondary btn-sm\",\n                        children: \"Voir la stack des composants\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 121,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"pre\", {\n                        className: \"mt-2 p-2 bg-light border rounded\",\n                        style: {\n                          fontSize: '0.8rem'\n                        },\n                        children: (_this$state$errorInfo = this.state.errorInfo) === null || _this$state$errorInfo === void 0 ? void 0 : _this$state$errorInfo.componentStack\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 124,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 120,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 103,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"mt-4\",\n                    children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                      className: \"btn btn-primary me-2\",\n                      onClick: this.handleRetry,\n                      children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                        className: \"fas fa-redo me-1\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 136,\n                        columnNumber: 25\n                      }, this), \"R\\xE9essayer\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 132,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                      className: \"btn btn-secondary me-2\",\n                      onClick: this.handleReload,\n                      children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                        className: \"fas fa-refresh me-1\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 143,\n                        columnNumber: 25\n                      }, this), \"Recharger la page\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 139,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                      className: \"btn btn-outline-primary\",\n                      onClick: () => window.history.back(),\n                      children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                        className: \"fas fa-arrow-left me-1\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 150,\n                        columnNumber: 25\n                      }, this), \"Retour\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 146,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 131,\n                    columnNumber: 21\n                  }, this), this.state.errorId && /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"mt-3\",\n                    children: /*#__PURE__*/_jsxDEV(\"small\", {\n                      className: \"text-muted\",\n                      children: [\"ID d'erreur: \", this.state.errorId]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 157,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 156,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 97,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 90,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 89,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 88,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 87,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 86,\n        columnNumber: 9\n      }, this);\n    }\n    return this.props.children;\n  }\n}\n\n// Higher-order component for wrapping components with error boundary\nexport const withErrorBoundary = (Component, fallback) => {\n  return function WrappedComponent(props) {\n    return /*#__PURE__*/_jsxDEV(ErrorBoundary, {\n      fallback: fallback,\n      children: /*#__PURE__*/_jsxDEV(Component, {\n        ...props\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 180,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 179,\n      columnNumber: 7\n    }, this);\n  };\n};\n\n// Hook for error handling in functional components\nexport const useErrorHandler = () => {\n  _s();\n  const [error, setError] = React.useState(null);\n  const resetError = () => setError(null);\n  const captureError = error => {\n    console.error('Error captured by useErrorHandler:', error);\n    setError(error);\n  };\n  React.useEffect(() => {\n    if (error) {\n      throw error;\n    }\n  }, [error]);\n  return {\n    captureError,\n    resetError\n  };\n};\n\n// Async error boundary for handling promise rejections\n_s(useErrorHandler, \"JfhGochNIqPkY17zyDsXnSE7zLQ=\");\nexport class AsyncErrorBoundary extends React.Component {\n  constructor(props) {\n    super(props);\n    this.handleUnhandledRejection = event => {\n      console.error('Unhandled promise rejection:', event.reason);\n      this.setState({\n        hasError: true,\n        error: event.reason\n      });\n      event.preventDefault();\n    };\n    this.state = {\n      hasError: false,\n      error: null\n    };\n  }\n  static getDerivedStateFromError(error) {\n    return {\n      hasError: true,\n      error\n    };\n  }\n  componentDidCatch(error, errorInfo) {\n    console.error('AsyncErrorBoundary caught an error:', error, errorInfo);\n  }\n  componentDidMount() {\n    // Listen for unhandled promise rejections\n    window.addEventListener('unhandledrejection', this.handleUnhandledRejection);\n  }\n  componentWillUnmount() {\n    window.removeEventListener('unhandledrejection', this.handleUnhandledRejection);\n  }\n  render() {\n    if (this.state.hasError) {\n      return /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"alert alert-danger\",\n        children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n          children: \"Erreur asynchrone d\\xE9tect\\xE9e\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 240,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"Une erreur s'est produite lors d'une op\\xE9ration asynchrone.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 241,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"btn btn-outline-danger\",\n          onClick: () => this.setState({\n            hasError: false,\n            error: null\n          }),\n          children: \"R\\xE9essayer\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 242,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 239,\n        columnNumber: 9\n      }, this);\n    }\n    return this.props.children;\n  }\n}\nexport default ErrorBoundary;", "map": {"version": 3, "names": ["React", "jsxDEV", "_jsxDEV", "Error<PERSON>ou<PERSON><PERSON>", "Component", "constructor", "props", "logErrorToService", "error", "errorInfo", "errorId", "errorData", "message", "stack", "componentStack", "timestamp", "Date", "toISOString", "userAgent", "navigator", "url", "window", "location", "href", "userId", "localStorage", "getItem", "JSON", "parse", "id", "console", "log", "loggingError", "handleRetry", "setState", "<PERSON><PERSON><PERSON><PERSON>", "handleReload", "reload", "state", "getDerivedStateFromError", "componentDidCatch", "now", "Math", "random", "toString", "substr", "render", "_this$state$error", "_this$state$error2", "_this$state$errorInfo", "fallback", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "process", "env", "NODE_ENV", "style", "fontSize", "onClick", "history", "back", "with<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "WrappedComponent", "useErrorHandler", "_s", "setError", "useState", "resetError", "captureError", "useEffect", "AsyncErrorBoundary", "handleUnhandledRejection", "event", "reason", "preventDefault", "componentDidMount", "addEventListener", "componentWillUnmount", "removeEventListener"], "sources": ["/Users/<USER>/Desktop/Projrt FINOPS/frontend/src/components/ErrorBoundary/ErrorBoundary.js"], "sourcesContent": ["import React from 'react';\n\nclass ErrorBoundary extends React.Component {\n  constructor(props) {\n    super(props);\n    this.state = { \n      hasError: false, \n      error: null, \n      errorInfo: null,\n      errorId: null\n    };\n  }\n\n  static getDerivedStateFromError(error) {\n    // Update state so the next render will show the fallback UI\n    return { hasError: true };\n  }\n\n  componentDidCatch(error, errorInfo) {\n    // Generate unique error ID for tracking\n    const errorId = `error_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;\n    \n    // Log error details\n    console.error('ErrorBoundary caught an error:', error, errorInfo);\n    \n    // Update state with error details\n    this.setState({\n      error,\n      errorInfo,\n      errorId\n    });\n\n    // Send error to logging service (if available)\n    this.logErrorToService(error, errorInfo, errorId);\n  }\n\n  logErrorToService = (error, errorInfo, errorId) => {\n    try {\n      // In a real application, you would send this to your logging service\n      const errorData = {\n        errorId,\n        message: error.message,\n        stack: error.stack,\n        componentStack: errorInfo.componentStack,\n        timestamp: new Date().toISOString(),\n        userAgent: navigator.userAgent,\n        url: window.location.href,\n        userId: localStorage.getItem('user') ? JSON.parse(localStorage.getItem('user')).id : null,\n      };\n\n      // Example: Send to your API\n      // fetch('/api/errors', {\n      //   method: 'POST',\n      //   headers: { 'Content-Type': 'application/json' },\n      //   body: JSON.stringify(errorData)\n      // });\n\n      console.log('Error logged:', errorData);\n    } catch (loggingError) {\n      console.error('Failed to log error:', loggingError);\n    }\n  };\n\n  handleRetry = () => {\n    this.setState({\n      hasError: false,\n      error: null,\n      errorInfo: null,\n      errorId: null\n    });\n  };\n\n  handleReload = () => {\n    window.location.reload();\n  };\n\n  render() {\n    if (this.state.hasError) {\n      // Custom fallback UI\n      if (this.props.fallback) {\n        return this.props.fallback(this.state.error, this.handleRetry);\n      }\n\n      // Default fallback UI\n      return (\n        <div className=\"error-boundary\">\n          <div className=\"container mt-5\">\n            <div className=\"row justify-content-center\">\n              <div className=\"col-md-8\">\n                <div className=\"card border-danger\">\n                  <div className=\"card-header bg-danger text-white\">\n                    <h4 className=\"mb-0\">\n                      <i className=\"fas fa-exclamation-triangle me-2\"></i>\n                      Une erreur s'est produite\n                    </h4>\n                  </div>\n                  <div className=\"card-body\">\n                    <p className=\"card-text\">\n                      Désolé, quelque chose s'est mal passé. L'erreur a été signalée à notre équipe.\n                    </p>\n                    \n                    {process.env.NODE_ENV === 'development' && (\n                      <div className=\"mt-3\">\n                        <h6>Détails de l'erreur (mode développement):</h6>\n                        <div className=\"alert alert-secondary\">\n                          <strong>Message:</strong> {this.state.error?.message}\n                          <br />\n                          <strong>ID d'erreur:</strong> {this.state.errorId}\n                        </div>\n                        \n                        <details className=\"mt-2\">\n                          <summary className=\"btn btn-outline-secondary btn-sm\">\n                            Voir la stack trace\n                          </summary>\n                          <pre className=\"mt-2 p-2 bg-light border rounded\" style={{ fontSize: '0.8rem' }}>\n                            {this.state.error?.stack}\n                          </pre>\n                        </details>\n                        \n                        <details className=\"mt-2\">\n                          <summary className=\"btn btn-outline-secondary btn-sm\">\n                            Voir la stack des composants\n                          </summary>\n                          <pre className=\"mt-2 p-2 bg-light border rounded\" style={{ fontSize: '0.8rem' }}>\n                            {this.state.errorInfo?.componentStack}\n                          </pre>\n                        </details>\n                      </div>\n                    )}\n                    \n                    <div className=\"mt-4\">\n                      <button \n                        className=\"btn btn-primary me-2\" \n                        onClick={this.handleRetry}\n                      >\n                        <i className=\"fas fa-redo me-1\"></i>\n                        Réessayer\n                      </button>\n                      <button \n                        className=\"btn btn-secondary me-2\" \n                        onClick={this.handleReload}\n                      >\n                        <i className=\"fas fa-refresh me-1\"></i>\n                        Recharger la page\n                      </button>\n                      <button \n                        className=\"btn btn-outline-primary\" \n                        onClick={() => window.history.back()}\n                      >\n                        <i className=\"fas fa-arrow-left me-1\"></i>\n                        Retour\n                      </button>\n                    </div>\n                    \n                    {this.state.errorId && (\n                      <div className=\"mt-3\">\n                        <small className=\"text-muted\">\n                          ID d'erreur: {this.state.errorId}\n                        </small>\n                      </div>\n                    )}\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      );\n    }\n\n    return this.props.children;\n  }\n}\n\n// Higher-order component for wrapping components with error boundary\nexport const withErrorBoundary = (Component, fallback) => {\n  return function WrappedComponent(props) {\n    return (\n      <ErrorBoundary fallback={fallback}>\n        <Component {...props} />\n      </ErrorBoundary>\n    );\n  };\n};\n\n// Hook for error handling in functional components\nexport const useErrorHandler = () => {\n  const [error, setError] = React.useState(null);\n\n  const resetError = () => setError(null);\n\n  const captureError = (error) => {\n    console.error('Error captured by useErrorHandler:', error);\n    setError(error);\n  };\n\n  React.useEffect(() => {\n    if (error) {\n      throw error;\n    }\n  }, [error]);\n\n  return { captureError, resetError };\n};\n\n// Async error boundary for handling promise rejections\nexport class AsyncErrorBoundary extends React.Component {\n  constructor(props) {\n    super(props);\n    this.state = { hasError: false, error: null };\n  }\n\n  static getDerivedStateFromError(error) {\n    return { hasError: true, error };\n  }\n\n  componentDidCatch(error, errorInfo) {\n    console.error('AsyncErrorBoundary caught an error:', error, errorInfo);\n  }\n\n  componentDidMount() {\n    // Listen for unhandled promise rejections\n    window.addEventListener('unhandledrejection', this.handleUnhandledRejection);\n  }\n\n  componentWillUnmount() {\n    window.removeEventListener('unhandledrejection', this.handleUnhandledRejection);\n  }\n\n  handleUnhandledRejection = (event) => {\n    console.error('Unhandled promise rejection:', event.reason);\n    this.setState({ hasError: true, error: event.reason });\n    event.preventDefault();\n  };\n\n  render() {\n    if (this.state.hasError) {\n      return (\n        <div className=\"alert alert-danger\">\n          <h5>Erreur asynchrone détectée</h5>\n          <p>Une erreur s'est produite lors d'une opération asynchrone.</p>\n          <button \n            className=\"btn btn-outline-danger\"\n            onClick={() => this.setState({ hasError: false, error: null })}\n          >\n            Réessayer\n          </button>\n        </div>\n      );\n    }\n\n    return this.props.children;\n  }\n}\n\nexport default ErrorBoundary;\n"], "mappings": ";;AAAA,OAAOA,KAAK,MAAM,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1B,MAAMC,aAAa,SAASH,KAAK,CAACI,SAAS,CAAC;EAC1CC,WAAWA,CAACC,KAAK,EAAE;IACjB,KAAK,CAACA,KAAK,CAAC;IAAC,KAgCfC,iBAAiB,GAAG,CAACC,KAAK,EAAEC,SAAS,EAAEC,OAAO,KAAK;MACjD,IAAI;QACF;QACA,MAAMC,SAAS,GAAG;UAChBD,OAAO;UACPE,OAAO,EAAEJ,KAAK,CAACI,OAAO;UACtBC,KAAK,EAAEL,KAAK,CAACK,KAAK;UAClBC,cAAc,EAAEL,SAAS,CAACK,cAAc;UACxCC,SAAS,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;UACnCC,SAAS,EAAEC,SAAS,CAACD,SAAS;UAC9BE,GAAG,EAAEC,MAAM,CAACC,QAAQ,CAACC,IAAI;UACzBC,MAAM,EAAEC,YAAY,CAACC,OAAO,CAAC,MAAM,CAAC,GAAGC,IAAI,CAACC,KAAK,CAACH,YAAY,CAACC,OAAO,CAAC,MAAM,CAAC,CAAC,CAACG,EAAE,GAAG;QACvF,CAAC;;QAED;QACA;QACA;QACA;QACA;QACA;;QAEAC,OAAO,CAACC,GAAG,CAAC,eAAe,EAAEpB,SAAS,CAAC;MACzC,CAAC,CAAC,OAAOqB,YAAY,EAAE;QACrBF,OAAO,CAACtB,KAAK,CAAC,sBAAsB,EAAEwB,YAAY,CAAC;MACrD;IACF,CAAC;IAAA,KAEDC,WAAW,GAAG,MAAM;MAClB,IAAI,CAACC,QAAQ,CAAC;QACZC,QAAQ,EAAE,KAAK;QACf3B,KAAK,EAAE,IAAI;QACXC,SAAS,EAAE,IAAI;QACfC,OAAO,EAAE;MACX,CAAC,CAAC;IACJ,CAAC;IAAA,KAED0B,YAAY,GAAG,MAAM;MACnBf,MAAM,CAACC,QAAQ,CAACe,MAAM,CAAC,CAAC;IAC1B,CAAC;IArEC,IAAI,CAACC,KAAK,GAAG;MACXH,QAAQ,EAAE,KAAK;MACf3B,KAAK,EAAE,IAAI;MACXC,SAAS,EAAE,IAAI;MACfC,OAAO,EAAE;IACX,CAAC;EACH;EAEA,OAAO6B,wBAAwBA,CAAC/B,KAAK,EAAE;IACrC;IACA,OAAO;MAAE2B,QAAQ,EAAE;IAAK,CAAC;EAC3B;EAEAK,iBAAiBA,CAAChC,KAAK,EAAEC,SAAS,EAAE;IAClC;IACA,MAAMC,OAAO,GAAG,SAASM,IAAI,CAACyB,GAAG,CAAC,CAAC,IAAIC,IAAI,CAACC,MAAM,CAAC,CAAC,CAACC,QAAQ,CAAC,EAAE,CAAC,CAACC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE;;IAEhF;IACAf,OAAO,CAACtB,KAAK,CAAC,gCAAgC,EAAEA,KAAK,EAAEC,SAAS,CAAC;;IAEjE;IACA,IAAI,CAACyB,QAAQ,CAAC;MACZ1B,KAAK;MACLC,SAAS;MACTC;IACF,CAAC,CAAC;;IAEF;IACA,IAAI,CAACH,iBAAiB,CAACC,KAAK,EAAEC,SAAS,EAAEC,OAAO,CAAC;EACnD;EA0CAoC,MAAMA,CAAA,EAAG;IACP,IAAI,IAAI,CAACR,KAAK,CAACH,QAAQ,EAAE;MAAA,IAAAY,iBAAA,EAAAC,kBAAA,EAAAC,qBAAA;MACvB;MACA,IAAI,IAAI,CAAC3C,KAAK,CAAC4C,QAAQ,EAAE;QACvB,OAAO,IAAI,CAAC5C,KAAK,CAAC4C,QAAQ,CAAC,IAAI,CAACZ,KAAK,CAAC9B,KAAK,EAAE,IAAI,CAACyB,WAAW,CAAC;MAChE;;MAEA;MACA,oBACE/B,OAAA;QAAKiD,SAAS,EAAC,gBAAgB;QAAAC,QAAA,eAC7BlD,OAAA;UAAKiD,SAAS,EAAC,gBAAgB;UAAAC,QAAA,eAC7BlD,OAAA;YAAKiD,SAAS,EAAC,4BAA4B;YAAAC,QAAA,eACzClD,OAAA;cAAKiD,SAAS,EAAC,UAAU;cAAAC,QAAA,eACvBlD,OAAA;gBAAKiD,SAAS,EAAC,oBAAoB;gBAAAC,QAAA,gBACjClD,OAAA;kBAAKiD,SAAS,EAAC,kCAAkC;kBAAAC,QAAA,eAC/ClD,OAAA;oBAAIiD,SAAS,EAAC,MAAM;oBAAAC,QAAA,gBAClBlD,OAAA;sBAAGiD,SAAS,EAAC;oBAAkC;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,6BAEtD;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACF,CAAC,eACNtD,OAAA;kBAAKiD,SAAS,EAAC,WAAW;kBAAAC,QAAA,gBACxBlD,OAAA;oBAAGiD,SAAS,EAAC,WAAW;oBAAAC,QAAA,EAAC;kBAEzB;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC,EAEHC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,aAAa,iBACrCzD,OAAA;oBAAKiD,SAAS,EAAC,MAAM;oBAAAC,QAAA,gBACnBlD,OAAA;sBAAAkD,QAAA,EAAI;oBAAyC;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eAClDtD,OAAA;sBAAKiD,SAAS,EAAC,uBAAuB;sBAAAC,QAAA,gBACpClD,OAAA;wBAAAkD,QAAA,EAAQ;sBAAQ;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC,KAAC,GAAAT,iBAAA,GAAC,IAAI,CAACT,KAAK,CAAC9B,KAAK,cAAAuC,iBAAA,uBAAhBA,iBAAA,CAAkBnC,OAAO,eACpDV,OAAA;wBAAAmD,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC,eACNtD,OAAA;wBAAAkD,QAAA,EAAQ;sBAAY;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC,KAAC,EAAC,IAAI,CAAClB,KAAK,CAAC5B,OAAO;oBAAA;sBAAA2C,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC9C,CAAC,eAENtD,OAAA;sBAASiD,SAAS,EAAC,MAAM;sBAAAC,QAAA,gBACvBlD,OAAA;wBAASiD,SAAS,EAAC,kCAAkC;wBAAAC,QAAA,EAAC;sBAEtD;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAS,CAAC,eACVtD,OAAA;wBAAKiD,SAAS,EAAC,kCAAkC;wBAACS,KAAK,EAAE;0BAAEC,QAAQ,EAAE;wBAAS,CAAE;wBAAAT,QAAA,GAAAJ,kBAAA,GAC7E,IAAI,CAACV,KAAK,CAAC9B,KAAK,cAAAwC,kBAAA,uBAAhBA,kBAAA,CAAkBnC;sBAAK;wBAAAwC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACrB,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACC,CAAC,eAEVtD,OAAA;sBAASiD,SAAS,EAAC,MAAM;sBAAAC,QAAA,gBACvBlD,OAAA;wBAASiD,SAAS,EAAC,kCAAkC;wBAAAC,QAAA,EAAC;sBAEtD;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAS,CAAC,eACVtD,OAAA;wBAAKiD,SAAS,EAAC,kCAAkC;wBAACS,KAAK,EAAE;0BAAEC,QAAQ,EAAE;wBAAS,CAAE;wBAAAT,QAAA,GAAAH,qBAAA,GAC7E,IAAI,CAACX,KAAK,CAAC7B,SAAS,cAAAwC,qBAAA,uBAApBA,qBAAA,CAAsBnC;sBAAc;wBAAAuC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAClC,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACC,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACP,CACN,eAEDtD,OAAA;oBAAKiD,SAAS,EAAC,MAAM;oBAAAC,QAAA,gBACnBlD,OAAA;sBACEiD,SAAS,EAAC,sBAAsB;sBAChCW,OAAO,EAAE,IAAI,CAAC7B,WAAY;sBAAAmB,QAAA,gBAE1BlD,OAAA;wBAAGiD,SAAS,EAAC;sBAAkB;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,gBAEtC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,eACTtD,OAAA;sBACEiD,SAAS,EAAC,wBAAwB;sBAClCW,OAAO,EAAE,IAAI,CAAC1B,YAAa;sBAAAgB,QAAA,gBAE3BlD,OAAA;wBAAGiD,SAAS,EAAC;sBAAqB;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,qBAEzC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,eACTtD,OAAA;sBACEiD,SAAS,EAAC,yBAAyB;sBACnCW,OAAO,EAAEA,CAAA,KAAMzC,MAAM,CAAC0C,OAAO,CAACC,IAAI,CAAC,CAAE;sBAAAZ,QAAA,gBAErClD,OAAA;wBAAGiD,SAAS,EAAC;sBAAwB;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,UAE5C;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACN,CAAC,EAEL,IAAI,CAAClB,KAAK,CAAC5B,OAAO,iBACjBR,OAAA;oBAAKiD,SAAS,EAAC,MAAM;oBAAAC,QAAA,eACnBlD,OAAA;sBAAOiD,SAAS,EAAC,YAAY;sBAAAC,QAAA,GAAC,eACf,EAAC,IAAI,CAACd,KAAK,CAAC5B,OAAO;oBAAA;sBAAA2C,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC3B;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACL,CACN;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAEV;IAEA,OAAO,IAAI,CAAClD,KAAK,CAAC8C,QAAQ;EAC5B;AACF;;AAEA;AACA,OAAO,MAAMa,iBAAiB,GAAGA,CAAC7D,SAAS,EAAE8C,QAAQ,KAAK;EACxD,OAAO,SAASgB,gBAAgBA,CAAC5D,KAAK,EAAE;IACtC,oBACEJ,OAAA,CAACC,aAAa;MAAC+C,QAAQ,EAAEA,QAAS;MAAAE,QAAA,eAChClD,OAAA,CAACE,SAAS;QAAA,GAAKE;MAAK;QAAA+C,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACX,CAAC;EAEpB,CAAC;AACH,CAAC;;AAED;AACA,OAAO,MAAMW,eAAe,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACnC,MAAM,CAAC5D,KAAK,EAAE6D,QAAQ,CAAC,GAAGrE,KAAK,CAACsE,QAAQ,CAAC,IAAI,CAAC;EAE9C,MAAMC,UAAU,GAAGA,CAAA,KAAMF,QAAQ,CAAC,IAAI,CAAC;EAEvC,MAAMG,YAAY,GAAIhE,KAAK,IAAK;IAC9BsB,OAAO,CAACtB,KAAK,CAAC,oCAAoC,EAAEA,KAAK,CAAC;IAC1D6D,QAAQ,CAAC7D,KAAK,CAAC;EACjB,CAAC;EAEDR,KAAK,CAACyE,SAAS,CAAC,MAAM;IACpB,IAAIjE,KAAK,EAAE;MACT,MAAMA,KAAK;IACb;EACF,CAAC,EAAE,CAACA,KAAK,CAAC,CAAC;EAEX,OAAO;IAAEgE,YAAY;IAAED;EAAW,CAAC;AACrC,CAAC;;AAED;AAAAH,EAAA,CAnBaD,eAAe;AAoB5B,OAAO,MAAMO,kBAAkB,SAAS1E,KAAK,CAACI,SAAS,CAAC;EACtDC,WAAWA,CAACC,KAAK,EAAE;IACjB,KAAK,CAACA,KAAK,CAAC;IAAC,KAqBfqE,wBAAwB,GAAIC,KAAK,IAAK;MACpC9C,OAAO,CAACtB,KAAK,CAAC,8BAA8B,EAAEoE,KAAK,CAACC,MAAM,CAAC;MAC3D,IAAI,CAAC3C,QAAQ,CAAC;QAAEC,QAAQ,EAAE,IAAI;QAAE3B,KAAK,EAAEoE,KAAK,CAACC;MAAO,CAAC,CAAC;MACtDD,KAAK,CAACE,cAAc,CAAC,CAAC;IACxB,CAAC;IAxBC,IAAI,CAACxC,KAAK,GAAG;MAAEH,QAAQ,EAAE,KAAK;MAAE3B,KAAK,EAAE;IAAK,CAAC;EAC/C;EAEA,OAAO+B,wBAAwBA,CAAC/B,KAAK,EAAE;IACrC,OAAO;MAAE2B,QAAQ,EAAE,IAAI;MAAE3B;IAAM,CAAC;EAClC;EAEAgC,iBAAiBA,CAAChC,KAAK,EAAEC,SAAS,EAAE;IAClCqB,OAAO,CAACtB,KAAK,CAAC,qCAAqC,EAAEA,KAAK,EAAEC,SAAS,CAAC;EACxE;EAEAsE,iBAAiBA,CAAA,EAAG;IAClB;IACA1D,MAAM,CAAC2D,gBAAgB,CAAC,oBAAoB,EAAE,IAAI,CAACL,wBAAwB,CAAC;EAC9E;EAEAM,oBAAoBA,CAAA,EAAG;IACrB5D,MAAM,CAAC6D,mBAAmB,CAAC,oBAAoB,EAAE,IAAI,CAACP,wBAAwB,CAAC;EACjF;EAQA7B,MAAMA,CAAA,EAAG;IACP,IAAI,IAAI,CAACR,KAAK,CAACH,QAAQ,EAAE;MACvB,oBACEjC,OAAA;QAAKiD,SAAS,EAAC,oBAAoB;QAAAC,QAAA,gBACjClD,OAAA;UAAAkD,QAAA,EAAI;QAA0B;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACnCtD,OAAA;UAAAkD,QAAA,EAAG;QAA0D;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eACjEtD,OAAA;UACEiD,SAAS,EAAC,wBAAwB;UAClCW,OAAO,EAAEA,CAAA,KAAM,IAAI,CAAC5B,QAAQ,CAAC;YAAEC,QAAQ,EAAE,KAAK;YAAE3B,KAAK,EAAE;UAAK,CAAC,CAAE;UAAA4C,QAAA,EAChE;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAEV;IAEA,OAAO,IAAI,CAAClD,KAAK,CAAC8C,QAAQ;EAC5B;AACF;AAEA,eAAejD,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
import axios from 'axios';

const API_URL = process.env.REACT_APP_API_URL || 'http://localhost:8080';

// Create axios instance with default config
const api = axios.create({
  baseURL: API_URL,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Add auth token to requests
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Handle auth errors
api.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response?.status === 401) {
      localStorage.removeItem('token');
      window.location.href = '/login';
    }
    return Promise.reject(error);
  }
);

const FinOpsService = {
  // Dashboard
  getDashboard: async () => {
    const response = await api.get('/api/finops/dashboard');
    return response.data;
  },

  // Cost Analysis
  getCostAnalysis: async (params = {}) => {
    const response = await api.get('/api/finops/cost-analysis', { params });
    return response.data;
  },

  // Budget Management
  getBudgets: async (params = {}) => {
    const response = await api.get('/api/finops/budgets', { params });
    return response.data;
  },

  createBudget: async (budgetData) => {
    const response = await api.post('/api/finops/budgets', budgetData);
    return response.data;
  },

  updateBudget: async (budgetId, budgetData) => {
    const response = await api.put(`/api/finops/budgets/${budgetId}`, budgetData);
    return response.data;
  },

  deleteBudget: async (budgetId) => {
    const response = await api.delete(`/api/finops/budgets/${budgetId}`);
    return response.data;
  },

  // Optimization Recommendations
  getOptimizationRecommendations: async () => {
    const response = await api.get('/api/finops/optimization-recommendations');
    return response.data;
  },

  // Cost Reports
  getCostReport: async (params = {}) => {
    const response = await api.get('/api/finops/cost-report', { params });
    return response.data;
  },

  downloadCostReport: async (format = 'csv') => {
    const response = await api.get(`/api/finops/cost-report?format=${format}`, {
      responseType: 'blob'
    });
    
    // Create download link
    const url = window.URL.createObjectURL(new Blob([response.data]));
    const link = document.createElement('a');
    link.href = url;
    link.setAttribute('download', `cost-report-${new Date().toISOString().split('T')[0]}.${format}`);
    document.body.appendChild(link);
    link.click();
    link.remove();
    window.URL.revokeObjectURL(url);
  },

  // Resource Management
  getResources: async (params = {}) => {
    const response = await api.get('/api/finops/resources', { params });
    return response.data;
  },

  // Cost Forecasting
  getCostForecast: async (params = {}) => {
    const response = await api.get('/api/finops/cost-forecast', { params });
    return response.data;
  },

  // Alerts
  getAlerts: async () => {
    const response = await api.get('/api/finops/alerts');
    return response.data;
  },

  markAlertAsRead: async (alertId) => {
    const response = await api.patch(`/api/finops/alerts/${alertId}/read`);
    return response.data;
  },

  // Health Check
  healthCheck: async () => {
    const response = await api.get('/health');
    return response.data;
  }
};

export default FinOpsService;

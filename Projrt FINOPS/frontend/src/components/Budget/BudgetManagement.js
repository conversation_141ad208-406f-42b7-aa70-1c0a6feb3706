import React, { useState, useEffect } from 'react';
import FinOpsService from '../../services/FinOpsService';

const BudgetManagement = () => {
  const [budgets, setBudgets] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [showCreateForm, setShowCreateForm] = useState(false);
  const [newBudget, setNewBudget] = useState({
    name: '',
    description: '',
    scope: {
      type: 'team',
      filters: {
        teams: ['']
      }
    },
    period: {
      type: 'monthly',
      startDate: '',
      endDate: ''
    },
    amounts: {
      total: '',
      currency: 'EUR'
    }
  });

  useEffect(() => {
    fetchBudgets();
  }, []);

  const fetchBudgets = async () => {
    try {
      setLoading(true);
      const data = await FinOpsService.getBudgets();
      setBudgets(data.budgets || []);
    } catch (error) {
      setError('Failed to load budgets');
      console.error('Budget error:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleCreateBudget = async (e) => {
    e.preventDefault();
    try {
      const budgetData = {
        ...newBudget,
        amounts: {
          ...newBudget.amounts,
          total: parseFloat(newBudget.amounts.total)
        }
      };
      
      await FinOpsService.createBudget(budgetData);
      setShowCreateForm(false);
      setNewBudget({
        name: '',
        description: '',
        scope: {
          type: 'team',
          filters: {
            teams: ['']
          }
        },
        period: {
          type: 'monthly',
          startDate: '',
          endDate: ''
        },
        amounts: {
          total: '',
          currency: 'EUR'
        }
      });
      fetchBudgets();
    } catch (error) {
      setError('Failed to create budget');
      console.error('Create budget error:', error);
    }
  };

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'EUR'
    }).format(amount || 0);
  };

  const getProgressPercentage = (spent, total) => {
    return Math.min((spent / total) * 100, 100);
  };

  const getProgressColor = (percentage) => {
    if (percentage >= 90) return 'bg-danger';
    if (percentage >= 75) return 'bg-warning';
    return 'bg-success';
  };

  if (loading) {
    return (
      <div className="d-flex justify-content-center align-items-center" style={{ height: '400px' }}>
        <div className="spinner-border text-primary" role="status">
          <span className="visually-hidden">Loading...</span>
        </div>
      </div>
    );
  }

  return (
    <div className="container-fluid py-4">
      <div className="row mb-4">
        <div className="col">
          <h1 className="h3 mb-0 text-gray-800">💰 Budget Management</h1>
          <p className="text-muted">Manage and monitor your budgets</p>
        </div>
        <div className="col-auto">
          <button
            className="btn btn-primary"
            onClick={() => setShowCreateForm(true)}
          >
            <i className="fas fa-plus me-1"></i>
            Create Budget
          </button>
        </div>
      </div>

      {error && (
        <div className="alert alert-danger" role="alert">
          {error}
        </div>
      )}

      {showCreateForm && (
        <div className="row mb-4">
          <div className="col-12">
            <div className="card">
              <div className="card-header">
                <h5 className="mb-0">Create New Budget</h5>
              </div>
              <div className="card-body">
                <form onSubmit={handleCreateBudget}>
                  <div className="row">
                    <div className="col-md-6">
                      <div className="mb-3">
                        <label className="form-label">Budget Name</label>
                        <input
                          type="text"
                          className="form-control"
                          value={newBudget.name}
                          onChange={(e) => setNewBudget({...newBudget, name: e.target.value})}
                          required
                        />
                      </div>
                    </div>
                    <div className="col-md-6">
                      <div className="mb-3">
                        <label className="form-label">Total Amount (EUR)</label>
                        <input
                          type="number"
                          className="form-control"
                          value={newBudget.amounts.total}
                          onChange={(e) => setNewBudget({
                            ...newBudget,
                            amounts: {...newBudget.amounts, total: e.target.value}
                          })}
                          required
                        />
                      </div>
                    </div>
                  </div>
                  <div className="mb-3">
                    <label className="form-label">Description</label>
                    <textarea
                      className="form-control"
                      rows="3"
                      value={newBudget.description}
                      onChange={(e) => setNewBudget({...newBudget, description: e.target.value})}
                    />
                  </div>
                  <div className="row">
                    <div className="col-md-4">
                      <div className="mb-3">
                        <label className="form-label">Team</label>
                        <input
                          type="text"
                          className="form-control"
                          value={newBudget.scope.filters.teams[0]}
                          onChange={(e) => setNewBudget({
                            ...newBudget,
                            scope: {
                              ...newBudget.scope,
                              filters: {teams: [e.target.value]}
                            }
                          })}
                          placeholder="Enter team name"
                        />
                      </div>
                    </div>
                    <div className="col-md-4">
                      <div className="mb-3">
                        <label className="form-label">Start Date</label>
                        <input
                          type="date"
                          className="form-control"
                          value={newBudget.period.startDate}
                          onChange={(e) => setNewBudget({
                            ...newBudget,
                            period: {...newBudget.period, startDate: e.target.value}
                          })}
                          required
                        />
                      </div>
                    </div>
                    <div className="col-md-4">
                      <div className="mb-3">
                        <label className="form-label">End Date</label>
                        <input
                          type="date"
                          className="form-control"
                          value={newBudget.period.endDate}
                          onChange={(e) => setNewBudget({
                            ...newBudget,
                            period: {...newBudget.period, endDate: e.target.value}
                          })}
                          required
                        />
                      </div>
                    </div>
                  </div>
                  <div className="d-flex gap-2">
                    <button type="submit" className="btn btn-primary">
                      Create Budget
                    </button>
                    <button
                      type="button"
                      className="btn btn-secondary"
                      onClick={() => setShowCreateForm(false)}
                    >
                      Cancel
                    </button>
                  </div>
                </form>
              </div>
            </div>
          </div>
        </div>
      )}

      <div className="row">
        <div className="col-12">
          <div className="card shadow">
            <div className="card-header py-3">
              <h6 className="m-0 font-weight-bold text-primary">Active Budgets</h6>
            </div>
            <div className="card-body">
              {budgets.length > 0 ? (
                <div className="row">
                  {budgets.map((budget, index) => (
                    <div key={index} className="col-md-6 col-lg-4 mb-4">
                      <div className="card h-100">
                        <div className="card-body">
                          <h5 className="card-title">{budget.name}</h5>
                          <p className="card-text text-muted">{budget.description}</p>
                          
                          <div className="mb-3">
                            <div className="d-flex justify-content-between mb-1">
                              <span>Spent</span>
                              <span>{formatCurrency(budget.currentSpend?.amount || 0)}</span>
                            </div>
                            <div className="d-flex justify-content-between mb-2">
                              <span>Budget</span>
                              <span>{formatCurrency(budget.amounts?.total || 0)}</span>
                            </div>
                            <div className="progress">
                              <div
                                className={`progress-bar ${getProgressColor(getProgressPercentage(budget.currentSpend?.amount || 0, budget.amounts?.total || 1))}`}
                                role="progressbar"
                                style={{ width: `${getProgressPercentage(budget.currentSpend?.amount || 0, budget.amounts?.total || 1)}%` }}
                              ></div>
                            </div>
                            <small className="text-muted">
                              {getProgressPercentage(budget.currentSpend?.amount || 0, budget.amounts?.total || 1).toFixed(1)}% used
                            </small>
                          </div>
                          
                          <div className="small text-muted">
                            <div>Period: {budget.period?.type}</div>
                            <div>Team: {budget.scope?.filters?.teams?.[0] || 'All'}</div>
                            <div>Status: <span className="badge bg-success">{budget.status}</span></div>
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-5">
                  <i className="fas fa-wallet fa-3x text-muted mb-3"></i>
                  <p className="text-muted">No budgets found. Create your first budget to get started.</p>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default BudgetManagement;

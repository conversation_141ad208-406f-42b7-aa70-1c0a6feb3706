import React, { useState, useEffect } from 'react';
import FinOpsService from '../../services/FinOpsService';

const CostAnalysis = () => {
  const [costData, setCostData] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    fetchCostAnalysis();
  }, []);

  const fetchCostAnalysis = async () => {
    try {
      setLoading(true);
      const data = await FinOpsService.getCostAnalysis();
      setCostData(data);
    } catch (error) {
      setError('Failed to load cost analysis data');
      console.error('Cost analysis error:', error);
    } finally {
      setLoading(false);
    }
  };

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'EUR'
    }).format(amount || 0);
  };

  if (loading) {
    return (
      <div className="d-flex justify-content-center align-items-center" style={{ height: '400px' }}>
        <div className="spinner-border text-primary" role="status">
          <span className="visually-hidden">Loading...</span>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="alert alert-danger" role="alert">
        <h4 className="alert-heading">Error!</h4>
        <p>{error}</p>
        <button className="btn btn-outline-danger" onClick={fetchCostAnalysis}>
          Try Again
        </button>
      </div>
    );
  }

  return (
    <div className="container-fluid py-4">
      <div className="row mb-4">
        <div className="col">
          <h1 className="h3 mb-0 text-gray-800">📊 Cost Analysis</h1>
          <p className="text-muted">Detailed cost breakdown and trends</p>
        </div>
      </div>

      <div className="row">
        <div className="col-12">
          <div className="card shadow">
            <div className="card-header py-3">
              <h6 className="m-0 font-weight-bold text-primary">Daily Cost Breakdown</h6>
            </div>
            <div className="card-body">
              {costData?.dailyCosts && costData.dailyCosts.length > 0 ? (
                <div className="table-responsive">
                  <table className="table table-striped">
                    <thead>
                      <tr>
                        <th>Date</th>
                        <th>Resource Type</th>
                        <th>Total Cost</th>
                        <th>CPU Cost</th>
                        <th>Memory Cost</th>
                        <th>Storage Cost</th>
                        <th>Network Cost</th>
                        <th>Count</th>
                      </tr>
                    </thead>
                    <tbody>
                      {costData.dailyCosts.map((cost, index) => (
                        <tr key={index}>
                          <td>{cost._id?.date}</td>
                          <td>
                            <span className="badge bg-secondary">
                              {cost._id?.resourceType || 'Unknown'}
                            </span>
                          </td>
                          <td className="fw-bold">{formatCurrency(cost.totalCost)}</td>
                          <td>{formatCurrency(cost.cpuCost)}</td>
                          <td>{formatCurrency(cost.memoryCost)}</td>
                          <td>{formatCurrency(cost.storageCost)}</td>
                          <td>{formatCurrency(cost.networkCost)}</td>
                          <td>{cost.count}</td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              ) : (
                <div className="text-center py-5">
                  <i className="fas fa-chart-line fa-3x text-muted mb-3"></i>
                  <p className="text-muted">No cost analysis data available</p>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CostAnalysis;

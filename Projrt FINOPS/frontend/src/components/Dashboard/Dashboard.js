import React, { useState, useEffect } from 'react';
import FinOpsService from '../../services/FinOpsService';

const Dashboard = () => {
  const [dashboardData, setDashboardData] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    fetchDashboardData();
  }, []);

  const fetchDashboardData = async () => {
    try {
      setLoading(true);
      const data = await FinOpsService.getDashboard();
      setDashboardData(data);
    } catch (error) {
      setError('Failed to load dashboard data');
      console.error('Dashboard error:', error);
    } finally {
      setLoading(false);
    }
  };

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'EUR'
    }).format(amount || 0);
  };

  if (loading) {
    return (
      <div className="d-flex justify-content-center align-items-center" style={{ height: '400px' }}>
        <div className="spinner-border text-primary" role="status">
          <span className="visually-hidden">Loading...</span>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="alert alert-danger" role="alert">
        <h4 className="alert-heading">Error!</h4>
        <p>{error}</p>
        <button className="btn btn-outline-danger" onClick={fetchDashboardData}>
          Try Again
        </button>
      </div>
    );
  }

  const { summary, costTrends, teamBreakdown, topResources, budgets } = dashboardData || {};

  return (
    <div className="container-fluid py-4">
      <div className="row mb-4">
        <div className="col">
          <h1 className="h3 mb-0 text-gray-800">💰 FinOps Dashboard</h1>
          <p className="text-muted">Financial Operations Overview</p>
        </div>
      </div>

      {/* Summary Cards */}
      <div className="row mb-4">
        <div className="col-xl-3 col-md-6 mb-4">
          <div className="card border-left-primary shadow h-100 py-2">
            <div className="card-body">
              <div className="row no-gutters align-items-center">
                <div className="col mr-2">
                  <div className="text-xs font-weight-bold text-primary text-uppercase mb-1">
                    Total Monthly Cost
                  </div>
                  <div className="h5 mb-0 font-weight-bold text-gray-800">
                    {formatCurrency(summary?.totalCost)}
                  </div>
                </div>
                <div className="col-auto">
                  <i className="fas fa-dollar-sign fa-2x text-gray-300"></i>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div className="col-xl-3 col-md-6 mb-4">
          <div className="card border-left-success shadow h-100 py-2">
            <div className="card-body">
              <div className="row no-gutters align-items-center">
                <div className="col mr-2">
                  <div className="text-xs font-weight-bold text-success text-uppercase mb-1">
                    CPU Cost
                  </div>
                  <div className="h5 mb-0 font-weight-bold text-gray-800">
                    {formatCurrency(summary?.cpuCost)}
                  </div>
                </div>
                <div className="col-auto">
                  <i className="fas fa-microchip fa-2x text-gray-300"></i>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div className="col-xl-3 col-md-6 mb-4">
          <div className="card border-left-info shadow h-100 py-2">
            <div className="card-body">
              <div className="row no-gutters align-items-center">
                <div className="col mr-2">
                  <div className="text-xs font-weight-bold text-info text-uppercase mb-1">
                    Memory Cost
                  </div>
                  <div className="h5 mb-0 font-weight-bold text-gray-800">
                    {formatCurrency(summary?.memoryCost)}
                  </div>
                </div>
                <div className="col-auto">
                  <i className="fas fa-memory fa-2x text-gray-300"></i>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div className="col-xl-3 col-md-6 mb-4">
          <div className="card border-left-warning shadow h-100 py-2">
            <div className="card-body">
              <div className="row no-gutters align-items-center">
                <div className="col mr-2">
                  <div className="text-xs font-weight-bold text-warning text-uppercase mb-1">
                    Storage Cost
                  </div>
                  <div className="h5 mb-0 font-weight-bold text-gray-800">
                    {formatCurrency(summary?.storageCost)}
                  </div>
                </div>
                <div className="col-auto">
                  <i className="fas fa-hdd fa-2x text-gray-300"></i>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Charts Row */}
      <div className="row mb-4">
        <div className="col-xl-8 col-lg-7">
          <div className="card shadow mb-4">
            <div className="card-header py-3 d-flex flex-row align-items-center justify-content-between">
              <h6 className="m-0 font-weight-bold text-primary">Cost Trends (Last 30 Days)</h6>
            </div>
            <div className="card-body">
              {costTrends && costTrends.length > 0 ? (
                <div className="table-responsive">
                  <table className="table table-sm">
                    <thead>
                      <tr>
                        <th>Date</th>
                        <th>Total Cost</th>
                        <th>CPU</th>
                        <th>Memory</th>
                        <th>Storage</th>
                      </tr>
                    </thead>
                    <tbody>
                      {costTrends.slice(0, 10).map((trend, index) => (
                        <tr key={index}>
                          <td>{trend._id?.period}</td>
                          <td>{formatCurrency(trend.totalCost)}</td>
                          <td>{formatCurrency(trend.cpuCost)}</td>
                          <td>{formatCurrency(trend.memoryCost)}</td>
                          <td>{formatCurrency(trend.storageCost)}</td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              ) : (
                <p className="text-muted">No cost trend data available</p>
              )}
            </div>
          </div>
        </div>

        <div className="col-xl-4 col-lg-5">
          <div className="card shadow mb-4">
            <div className="card-header py-3 d-flex flex-row align-items-center justify-content-between">
              <h6 className="m-0 font-weight-bold text-primary">Team Breakdown</h6>
            </div>
            <div className="card-body">
              {teamBreakdown && teamBreakdown.length > 0 ? (
                <div>
                  {teamBreakdown.slice(0, 5).map((team, index) => (
                    <div key={index} className="mb-3">
                      <div className="d-flex justify-content-between">
                        <span className="small font-weight-bold">
                          {team._id?.team || 'Unknown'} ({team._id?.environment})
                        </span>
                        <span className="small">{formatCurrency(team.totalCost)}</span>
                      </div>
                      <div className="progress" style={{ height: '6px' }}>
                        <div
                          className="progress-bar bg-primary"
                          role="progressbar"
                          style={{ width: `${Math.min((team.totalCost / (teamBreakdown[0]?.totalCost || 1)) * 100, 100)}%` }}
                        ></div>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <p className="text-muted">No team data available</p>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Top Resources and Budgets */}
      <div className="row">
        <div className="col-lg-6 mb-4">
          <div className="card shadow">
            <div className="card-header py-3">
              <h6 className="m-0 font-weight-bold text-primary">Top Resources by Cost</h6>
            </div>
            <div className="card-body">
              {topResources && topResources.length > 0 ? (
                <div className="table-responsive">
                  <table className="table table-sm">
                    <thead>
                      <tr>
                        <th>Resource</th>
                        <th>Type</th>
                        <th>Cost</th>
                      </tr>
                    </thead>
                    <tbody>
                      {topResources.slice(0, 5).map((resource, index) => (
                        <tr key={index}>
                          <td className="small">{resource._id?.resourceName || 'Unknown'}</td>
                          <td className="small">{resource._id?.resourceType}</td>
                          <td className="small">{formatCurrency(resource.totalCost)}</td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              ) : (
                <p className="text-muted">No resource data available</p>
              )}
            </div>
          </div>
        </div>

        <div className="col-lg-6 mb-4">
          <div className="card shadow">
            <div className="card-header py-3">
              <h6 className="m-0 font-weight-bold text-primary">Active Budgets</h6>
            </div>
            <div className="card-body">
              {budgets && budgets.length > 0 ? (
                <div>
                  {budgets.slice(0, 3).map((budget, index) => (
                    <div key={index} className="mb-3">
                      <div className="d-flex justify-content-between">
                        <span className="small font-weight-bold">{budget.name}</span>
                        <span className="small">{formatCurrency(budget.amounts?.total)}</span>
                      </div>
                      <div className="progress" style={{ height: '6px' }}>
                        <div
                          className="progress-bar bg-success"
                          role="progressbar"
                          style={{ width: `${Math.min((budget.currentSpend?.amount || 0) / (budget.amounts?.total || 1) * 100, 100)}%` }}
                        ></div>
                      </div>
                      <small className="text-muted">
                        Spent: {formatCurrency(budget.currentSpend?.amount)} / {formatCurrency(budget.amounts?.total)}
                      </small>
                    </div>
                  ))}
                </div>
              ) : (
                <p className="text-muted">No active budgets</p>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Dashboard;

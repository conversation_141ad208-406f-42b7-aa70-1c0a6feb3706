<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>💰 FinOps Dashboard</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .dashboard-card {
            transition: transform 0.2s;
            border: none;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .dashboard-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 20px rgba(0,0,0,0.15);
        }
        .metric-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px;
        }
        .cost-card {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            color: white;
            border-radius: 15px;
        }
        .budget-card {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            border-radius: 15px;
        }
        .navbar-brand {
            font-weight: bold;
            font-size: 1.5rem;
        }
        .progress-bar {
            transition: width 0.6s ease;
        }
        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid #3498db;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        .status-indicator {
            width: 10px;
            height: 10px;
            border-radius: 50%;
            display: inline-block;
            margin-right: 5px;
        }
        .status-online { background-color: #28a745; }
        .status-offline { background-color: #dc3545; }
        .data-table {
            font-size: 0.9rem;
        }
        .currency {
            font-weight: bold;
            color: #28a745;
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container-fluid">
            <a class="navbar-brand" href="#">
                <i class="fas fa-chart-line me-2"></i>💰 FinOps Dashboard
            </a>
            <div class="navbar-nav ms-auto">
                <div class="nav-item">
                    <span class="nav-link">
                        <span id="connectionStatus" class="status-indicator status-offline"></span>
                        <span id="connectionText">Connecting...</span>
                    </span>
                </div>
                <div class="nav-item">
                    <span class="nav-link" id="userInfo">
                        <i class="fas fa-user me-1"></i>
                        <span id="userName">Guest</span>
                    </span>
                </div>
            </div>
        </div>
    </nav>

    <div class="container-fluid py-4">
        <!-- Login Section -->
        <div id="loginSection" class="row justify-content-center">
            <div class="col-md-6 col-lg-4">
                <div class="card dashboard-card">
                    <div class="card-body p-5">
                        <div class="text-center mb-4">
                            <h2 class="text-primary">💰 FinOps Login</h2>
                            <p class="text-muted">Financial Operations Management</p>
                        </div>

                        <div id="loginError" class="alert alert-danger d-none" role="alert"></div>

                        <form id="loginForm">
                            <div class="mb-3">
                                <label for="identifier" class="form-label">Email or Username</label>
                                <input type="text" class="form-control" id="identifier" required 
                                       placeholder="Enter your email or username" value="<EMAIL>">
                            </div>
                            <div class="mb-3">
                                <label for="password" class="form-label">Password</label>
                                <input type="password" class="form-control" id="password" required 
                                       placeholder="Enter your password" value="FinOpsAdmin123!">
                            </div>
                            <button type="submit" class="btn btn-primary w-100" id="loginBtn">
                                <span id="loginSpinner" class="loading d-none"></span>
                                Sign In
                            </button>
                        </form>

                        <div class="mt-4 p-3 bg-light rounded">
                            <h6 class="text-muted mb-2">Demo Credentials:</h6>
                            <small class="text-muted">
                                <strong>Admin:</strong> <EMAIL> / FinOpsAdmin123!<br>
                                <strong>User:</strong> <EMAIL> / TestPass123!
                            </small>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Dashboard Section -->
        <div id="dashboardSection" class="d-none">
            <!-- Summary Cards -->
            <div class="row mb-4">
                <div class="col-xl-3 col-md-6 mb-4">
                    <div class="card metric-card dashboard-card h-100">
                        <div class="card-body">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <div class="text-white-50 small">Total Monthly Cost</div>
                                    <div class="h4 mb-0 text-white" id="totalCost">€0.00</div>
                                </div>
                                <div class="text-white-50">
                                    <i class="fas fa-euro-sign fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-xl-3 col-md-6 mb-4">
                    <div class="card cost-card dashboard-card h-100">
                        <div class="card-body">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <div class="text-white-50 small">CPU Cost</div>
                                    <div class="h4 mb-0 text-white" id="cpuCost">€0.00</div>
                                </div>
                                <div class="text-white-50">
                                    <i class="fas fa-microchip fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-xl-3 col-md-6 mb-4">
                    <div class="card budget-card dashboard-card h-100">
                        <div class="card-body">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <div class="text-white-50 small">Memory Cost</div>
                                    <div class="h4 mb-0 text-white" id="memoryCost">€0.00</div>
                                </div>
                                <div class="text-white-50">
                                    <i class="fas fa-memory fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-xl-3 col-md-6 mb-4">
                    <div class="card dashboard-card h-100" style="background: linear-gradient(135deg, #fa709a 0%, #fee140 100%); color: white;">
                        <div class="card-body">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <div class="text-white-50 small">Storage Cost</div>
                                    <div class="h4 mb-0 text-white" id="storageCost">€0.00</div>
                                </div>
                                <div class="text-white-50">
                                    <i class="fas fa-hdd fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Cost Trends and Team Breakdown -->
            <div class="row mb-4">
                <div class="col-lg-8">
                    <div class="card dashboard-card h-100">
                        <div class="card-header bg-white">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-chart-line text-primary me-2"></i>Cost Trends (Last 30 Days)
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-hover data-table">
                                    <thead class="table-light">
                                        <tr>
                                            <th>Date</th>
                                            <th>Total Cost</th>
                                            <th>CPU</th>
                                            <th>Memory</th>
                                            <th>Storage</th>
                                            <th>Network</th>
                                        </tr>
                                    </thead>
                                    <tbody id="costTrendsTable">
                                        <tr>
                                            <td colspan="6" class="text-center">
                                                <div class="loading"></div> Loading cost trends...
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-lg-4">
                    <div class="card dashboard-card h-100">
                        <div class="card-header bg-white">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-users text-success me-2"></i>Team Breakdown
                            </h5>
                        </div>
                        <div class="card-body" id="teamBreakdown">
                            <div class="text-center">
                                <div class="loading"></div>
                                <p class="mt-2">Loading team data...</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Top Resources and Active Budgets -->
            <div class="row">
                <div class="col-lg-6">
                    <div class="card dashboard-card h-100">
                        <div class="card-header bg-white">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-server text-warning me-2"></i>Top Resources by Cost
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-sm data-table">
                                    <thead class="table-light">
                                        <tr>
                                            <th>Resource</th>
                                            <th>Type</th>
                                            <th>Cost</th>
                                            <th>CPU %</th>
                                            <th>Memory %</th>
                                        </tr>
                                    </thead>
                                    <tbody id="topResourcesTable">
                                        <tr>
                                            <td colspan="5" class="text-center">
                                                <div class="loading"></div> Loading resources...
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-lg-6">
                    <div class="card dashboard-card h-100">
                        <div class="card-header bg-white">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-wallet text-info me-2"></i>Active Budgets
                            </h5>
                        </div>
                        <div class="card-body" id="activeBudgets">
                            <div class="text-center">
                                <div class="loading"></div>
                                <p class="mt-2">Loading budgets...</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Configuration
        const API_BASE_URL = 'http://localhost:8080';
        let authToken = localStorage.getItem('finops_token');
        let currentUser = null;

        // DOM Elements
        const loginSection = document.getElementById('loginSection');
        const dashboardSection = document.getElementById('dashboardSection');
        const loginForm = document.getElementById('loginForm');
        const loginError = document.getElementById('loginError');
        const loginBtn = document.getElementById('loginBtn');
        const loginSpinner = document.getElementById('loginSpinner');
        const connectionStatus = document.getElementById('connectionStatus');
        const connectionText = document.getElementById('connectionText');
        const userName = document.getElementById('userName');

        // Utility Functions
        function formatCurrency(amount) {
            return new Intl.NumberFormat('en-EU', {
                style: 'currency',
                currency: 'EUR',
                minimumFractionDigits: 2
            }).format(amount || 0);
        }

        function formatPercentage(value) {
            return `${(value || 0).toFixed(1)}%`;
        }

        function showError(message) {
            loginError.textContent = message;
            loginError.classList.remove('d-none');
        }

        function hideError() {
            loginError.classList.add('d-none');
        }

        function setConnectionStatus(online) {
            if (online) {
                connectionStatus.className = 'status-indicator status-online';
                connectionText.textContent = 'Connected';
            } else {
                connectionStatus.className = 'status-indicator status-offline';
                connectionText.textContent = 'Offline';
            }
        }

        // API Functions
        async function makeApiCall(endpoint, options = {}) {
            try {
                const url = `${API_BASE_URL}${endpoint}`;
                const config = {
                    headers: {
                        'Content-Type': 'application/json',
                        ...options.headers
                    },
                    ...options
                };

                if (authToken) {
                    config.headers.Authorization = `Bearer ${authToken}`;
                }

                const response = await fetch(url, config);
                const data = await response.json();

                if (!response.ok) {
                    throw new Error(data.message || `HTTP ${response.status}`);
                }

                setConnectionStatus(true);
                return data;
            } catch (error) {
                console.error('API call failed:', error);
                setConnectionStatus(false);
                throw error;
            }
        }

        async function login(identifier, password) {
            const response = await makeApiCall('/api/auth/login', {
                method: 'POST',
                body: JSON.stringify({ identifier, password })
            });

            if (response.success) {
                authToken = response.data.token;
                currentUser = response.data.user;
                localStorage.setItem('finops_token', authToken);
                localStorage.setItem('finops_user', JSON.stringify(currentUser));
                return response.data;
            } else {
                throw new Error(response.message || 'Login failed');
            }
        }

        async function getDashboardData() {
            return await makeApiCall('/api/finops/dashboard');
        }

        async function checkBackendHealth() {
            try {
                await makeApiCall('/health');
                return true;
            } catch (error) {
                return false;
            }
        }

        // Dashboard Functions
        function updateSummaryCards(data) {
            const summary = data.data.summary;
            document.getElementById('totalCost').textContent = formatCurrency(summary.totalCost);
            document.getElementById('cpuCost').textContent = formatCurrency(summary.cpuCost);
            document.getElementById('memoryCost').textContent = formatCurrency(summary.memoryCost);
            document.getElementById('storageCost').textContent = formatCurrency(summary.storageCost);
        }

        function updateCostTrends(trends) {
            const tbody = document.getElementById('costTrendsTable');
            tbody.innerHTML = '';

            if (!trends || trends.length === 0) {
                tbody.innerHTML = '<tr><td colspan="6" class="text-center text-muted">No cost data available</td></tr>';
                return;
            }

            trends.slice(0, 10).forEach(trend => {
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td>${new Date(trend._id.period).toLocaleDateString()}</td>
                    <td class="currency">${formatCurrency(trend.totalCost)}</td>
                    <td>${formatCurrency(trend.cpuCost)}</td>
                    <td>${formatCurrency(trend.memoryCost)}</td>
                    <td>${formatCurrency(trend.storageCost)}</td>
                    <td>${formatCurrency(trend.networkCost)}</td>
                `;
                tbody.appendChild(row);
            });
        }

        function updateTeamBreakdown(teams) {
            const container = document.getElementById('teamBreakdown');
            container.innerHTML = '';

            if (!teams || teams.length === 0) {
                container.innerHTML = '<p class="text-muted text-center">No team data available</p>';
                return;
            }

            teams.slice(0, 5).forEach(team => {
                const percentage = Math.min((team.totalCost / teams[0].totalCost) * 100, 100);
                const teamElement = document.createElement('div');
                teamElement.className = 'mb-3';
                teamElement.innerHTML = `
                    <div class="d-flex justify-content-between align-items-center mb-1">
                        <span class="fw-bold">${team._id.team || 'Unknown'}</span>
                        <span class="currency">${formatCurrency(team.totalCost)}</span>
                    </div>
                    <div class="progress" style="height: 8px;">
                        <div class="progress-bar bg-success" role="progressbar"
                             style="width: ${percentage}%" aria-valuenow="${percentage}"
                             aria-valuemin="0" aria-valuemax="100"></div>
                    </div>
                    <small class="text-muted">${team._id.environment || 'N/A'} • ${team.uniqueResourceCount || 0} resources</small>
                `;
                container.appendChild(teamElement);
            });
        }

        function updateTopResources(resources) {
            const tbody = document.getElementById('topResourcesTable');
            tbody.innerHTML = '';

            if (!resources || resources.length === 0) {
                tbody.innerHTML = '<tr><td colspan="5" class="text-center text-muted">No resource data available</td></tr>';
                return;
            }

            resources.slice(0, 8).forEach(resource => {
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td>
                        <div class="fw-bold">${resource._id.resourceName || 'Unknown'}</div>
                        <small class="text-muted">${resource._id.namespace || 'default'}</small>
                    </td>
                    <td><span class="badge bg-secondary">${resource._id.resourceType || 'N/A'}</span></td>
                    <td class="currency">${formatCurrency(resource.totalCost)}</td>
                    <td>${formatPercentage(resource.avgCpuUtilization)}</td>
                    <td>${formatPercentage(resource.avgMemoryUtilization)}</td>
                `;
                tbody.appendChild(row);
            });
        }

        function updateActiveBudgets(budgets) {
            const container = document.getElementById('activeBudgets');
            container.innerHTML = '';

            if (!budgets || budgets.length === 0) {
                container.innerHTML = '<p class="text-muted text-center">No active budgets</p>';
                return;
            }

            budgets.slice(0, 4).forEach(budget => {
                const spent = budget.currentSpend?.amount || 0;
                const total = budget.amounts?.total || 1;
                const percentage = Math.min((spent / total) * 100, 100);
                const alertClass = percentage > 90 ? 'bg-danger' : percentage > 75 ? 'bg-warning' : 'bg-success';

                const budgetElement = document.createElement('div');
                budgetElement.className = 'mb-3';
                budgetElement.innerHTML = `
                    <div class="d-flex justify-content-between align-items-center mb-1">
                        <span class="fw-bold">${budget.name}</span>
                        <span class="currency">${formatCurrency(spent)} / ${formatCurrency(total)}</span>
                    </div>
                    <div class="progress" style="height: 8px;">
                        <div class="progress-bar ${alertClass}" role="progressbar"
                             style="width: ${percentage}%" aria-valuenow="${percentage}"
                             aria-valuemin="0" aria-valuemax="100"></div>
                    </div>
                    <small class="text-muted">${budget.scope?.type || 'Global'} • ${formatPercentage(percentage)} used</small>
                `;
                container.appendChild(budgetElement);
            });
        }

        async function loadDashboard() {
            try {
                const data = await getDashboardData();

                if (data.success) {
                    updateSummaryCards(data);
                    updateCostTrends(data.data.costTrends);
                    updateTeamBreakdown(data.data.teamBreakdown);
                    updateTopResources(data.data.topResources);
                    updateActiveBudgets(data.data.activeBudgets);
                } else {
                    console.error('Failed to load dashboard data:', data.message);
                }
            } catch (error) {
                console.error('Error loading dashboard:', error);
                // Show error state but don't logout
            }
        }

        function showDashboard() {
            loginSection.classList.add('d-none');
            dashboardSection.classList.remove('d-none');
            userName.textContent = currentUser?.firstName || currentUser?.username || 'User';
            loadDashboard();
        }

        function showLogin() {
            loginSection.classList.remove('d-none');
            dashboardSection.classList.add('d-none');
            userName.textContent = 'Guest';
        }

        function logout() {
            authToken = null;
            currentUser = null;
            localStorage.removeItem('finops_token');
            localStorage.removeItem('finops_user');
            showLogin();
        }

        // Event Handlers
        loginForm.addEventListener('submit', async (e) => {
            e.preventDefault();

            const identifier = document.getElementById('identifier').value;
            const password = document.getElementById('password').value;

            if (!identifier || !password) {
                showError('Please enter both email/username and password');
                return;
            }

            loginBtn.disabled = true;
            loginSpinner.classList.remove('d-none');
            hideError();

            try {
                await login(identifier, password);
                showDashboard();
            } catch (error) {
                showError(error.message || 'Login failed. Please check your credentials.');
            } finally {
                loginBtn.disabled = false;
                loginSpinner.classList.add('d-none');
            }
        });

        // Add logout functionality to user info
        document.getElementById('userInfo').addEventListener('click', () => {
            if (confirm('Are you sure you want to logout?')) {
                logout();
            }
        });

        // Auto-refresh dashboard data every 30 seconds
        setInterval(() => {
            if (authToken && !dashboardSection.classList.contains('d-none')) {
                loadDashboard();
            }
        }, 30000);

        // Check backend health periodically
        setInterval(async () => {
            const isHealthy = await checkBackendHealth();
            setConnectionStatus(isHealthy);
        }, 10000);

        // Initialize Application
        async function init() {
            // Check if user is already logged in
            const savedUser = localStorage.getItem('finops_user');
            if (authToken && savedUser) {
                try {
                    currentUser = JSON.parse(savedUser);
                    // Verify token is still valid by making a test API call
                    await getDashboardData();
                    showDashboard();
                } catch (error) {
                    // Token is invalid, clear storage and show login
                    logout();
                }
            } else {
                showLogin();
            }

            // Initial backend health check
            const isHealthy = await checkBackendHealth();
            setConnectionStatus(isHealthy);
        }

        // Start the application
        document.addEventListener('DOMContentLoaded', init);

        // Add some demo functionality for testing
        window.finopsDemo = {
            login: () => {
                document.getElementById('identifier').value = '<EMAIL>';
                document.getElementById('password').value = 'FinOpsAdmin123!';
                loginForm.dispatchEvent(new Event('submit'));
            },
            logout: logout,
            refresh: loadDashboard,
            testApi: async () => {
                try {
                    const health = await makeApiCall('/health');
                    console.log('Backend Health:', health);

                    if (authToken) {
                        const dashboard = await makeApiCall('/api/finops/dashboard');
                        console.log('Dashboard Data:', dashboard);
                    }
                } catch (error) {
                    console.error('API Test Failed:', error);
                }
            }
        };

        console.log('💰 FinOps Dashboard Loaded!');
        console.log('Demo functions available: finopsDemo.login(), finopsDemo.logout(), finopsDemo.refresh(), finopsDemo.testApi()');
    </script>
</body>
</html>
